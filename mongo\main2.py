import asyncio
from decimal import Decimal
from itertools import chain, repeat
import json
import math
import random
import re
import sys
from bson import ObjectId
from pymongo import MongoClient, ReturnDocument, UpdateOne, InsertOne
from pymongo.errors import ConnectionFailure, BulkWriteError
from datetime import datetime, timedelta
import pytz
import gc
import psutil
import platform
import time
import logging
import zstandard as zstd
import pickle
from pathlib import Path

# Enable logging
# logging.basicConfig(level=logging.DEBUG)

# Set the event loop policy to use SelectorEventLoop on Windows
if platform.system() == "Windows":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

""" import time
import sys
import json
import os """
import traceback

from bot_soup2 import SoupBot2

# Initialize the MongoDB client and databases
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0&compressors=zstd",
    compressors="zstd",
)

log_db = client["log"]
exam_db = client["exam"]
qna_db = client["qna"]
# Detail collection under 'exam' database
exam_detail_collection = exam_db["detail"]
# Vendor collection under 'exam' database
vendor_collection = exam_db["vendor"]

# Define Hong Kong timezone
tz = pytz.timezone("Asia/Hong_Kong")

bot_soup = SoupBot2()

# Create a cache directory if it doesn't exist
cache_dir = Path("cache")
cache_dir.mkdir(exist_ok=True)

# Function to get cached discussions or scrape new ones


async def get_vendor_discussions(provider_slug, LAST_PAGE):
    cache_file = cache_dir / f"{provider_slug}_discussions.pkl"

    # Check if cached discussions exist
    if cache_file.exists():
        try:
            with open(cache_file, "rb") as f:
                whole_discussions = pickle.load(f)
                print(
                    f"Loaded cached discussions for {provider_slug} ({len(whole_discussions)} links)")
                return whole_discussions
        except Exception as e:
            print(f"Error loading cached discussions: {e}")

    # If no cache or error, scrape discussions
    print(f"Scraping discussions for {provider_slug}...")
    whole_discussions = await bot_soup.process_multiple_pages(
        [f"discussions/{provider_slug}/{x}" for x in range(1, LAST_PAGE + 1)],
        bot_soup.get_discussion_links_per_page,
    )
    print("Packing whole_discussions array...")
    whole_discussions = [
        item
        for sublist in whole_discussions
        if sublist is not None
        for item in sublist
    ]

    # Cache the discussions
    try:
        with open(cache_file, "wb") as f:
            pickle.dump(whole_discussions, f)
        print(
            f"Cached {len(whole_discussions)} discussion links for {provider_slug}")
    except Exception as e:
        print(f"Error caching discussions: {e}")

    return whole_discussions


def custom_converter(o):
    if isinstance(o, datetime):
        return o.isoformat()  # Convert datetime to ISO format
    elif isinstance(o, set):
        return list(o)  # Convert set to list
    elif isinstance(o, bytes):
        return o.decode("utf-8")  # Convert bytes to string
    elif isinstance(o, Decimal):
        return float(o)  # Convert Decimal to float
    elif isinstance(o, complex):
        return {"real": o.real, "imag": o.imag}  # Convert complex to dict
    # Add more types as needed
    raise TypeError(f"Object of type {type(o)} is not JSON serializable")


# Compressing data using ZSTD
def compress_data(data):
    try:
        json_data = json.dumps(data, default=custom_converter)
    except TypeError as e:
        raise TypeError(f"Data is not JSON serializable: {e}")

    compressor = zstd.ZstdCompressor()
    return compressor.compress(json_data.encode("utf-8"))


def custom_decoder(dct):
    # Convert ISO formatted datetime strings back to datetime objects
    for key, value in dct.items():
        if isinstance(value, str):
            try:
                dct[key] = datetime.fromisoformat(
                    value)  # Attempt to parse datetime
            except ValueError:
                pass  # Not a datetime string
        elif isinstance(value, list) and len(value) == 2:
            # Check if the list represents a complex number
            if all(isinstance(v, (int, float)) for v in value):
                # Convert back to complex
                dct[key] = complex(value[0], value[1])
    return dct


# Decompressing data using ZSTD
def decompress_data(compressed_data):
    # Handle empty input
    if not compressed_data:
        return {}  # Or return an empty dict: return {}

    # Ensure the input is bytes
    if not isinstance(compressed_data, bytes):
        return compressed_data

    decompressor = zstd.ZstdDecompressor()
    json_data = decompressor.decompress(compressed_data).decode("utf-8")

    try:
        return json.loads(json_data, object_hook=custom_decoder)
    except json.JSONDecodeError as e:
        raise ValueError(f"Failed to decode JSON: {e}")


def log_memory_usage(event):
    memory_info = psutil.virtual_memory()
    print(f"Memory usage: {memory_info.percent}%")
    log_action("memory", event, percent=memory_info.percent)


def save_state(**kwargs):
    """Save the current state to MongoDB."""
    init_state = {
        "date": datetime.now(tz).strftime("%Y-%m-%d"),
        "timestamp": datetime.now(tz),
    }

    # Check each keyword argument
    for key, value in kwargs.items():
        if isinstance(value, set):  # Check if the value is a set
            kwargs[key] = list(value)  # Convert set to list

    # Update state with any keyword arguments provided
    init_state.update(kwargs)

    log_db["state"].update_one(
        {"date": init_state["date"]},
        {"$set": init_state},
        upsert=True,  # Create a new document if it doesn't exist
    )


def load_state():
    loaded_state = log_db["state"].find_one(
        {"date": datetime.now(tz).strftime("%Y-%m-%d")}
    )

    if loaded_state is None:
        loaded_state = {}  # Default to an empty dictionary

    return loaded_state


def log_action(collection_name, action, **kwargs):
    log_db[collection_name].insert_one(
        {"action": action, "timestamp": datetime.now(tz), **kwargs}
    )


exams_to_update_or_create = {}


def compare_exam_lists_with_conditions(records, scrapped, popular_exams):
    # Create a dictionary for records based on code
    records_dict = {item["code"]: item for item in records}
    exam_detail_collection_operations = []
    total_questions_counts = []
    # Loop through scrapped to compare with records
    x = 1
    for scrapped_item in scrapped:
        print(f"compare_exam_lists_with_conditions: {x} of {len(scrapped)}")
        x += 1
        if scrapped_item is None:
            print(f"None!!!!!!!!!! Continue!")
            log_action("exception", "scrapped_item None")
            continue
        code = scrapped_item["code"]
        if code in records_dict:  # Check if code exists in records_dict
            record = records_dict[code]
            """ if code=="csm-001":
                print(record, scrapped_item)
                exit() """

            total_questions_count = qna_db[
                scrapped_item["vendor_code"]
            ].count_documents({"code": code})

            vendor_code = scrapped_item["vendor_code"]
            # Check if claimed_total and date_updated are not identical
            if record.get("claimed_total") != scrapped_item["claimed_total"] or record.get("date_updated") != scrapped_item["date_updated"]:
                """ P.S. The date_updated is most probably faked """
                exam_detail_collection_operations.append(
                    UpdateOne(
                        {
                            "code": code,
                        },
                        {
                            "$set": {
                                # "vendor": vendor_id,
                                "vendor_code": scrapped_item["vendor_code"],
                                "name": scrapped_item["name"],
                                "is_popular": code in popular_exams,
                                "claimed_total": scrapped_item["claimed_total"],
                                "total_questions": total_questions_count,
                                "total_questions_mc_or_mc2": # we just handle mc and mc2 on frontend at the moment. Use this digit for user.
                                    qna_db[scrapped_item["vendor_code"]].count_documents({
                                        "code": code,
                                        "subject": {"$exists": True},
                                        "type": {"$in": ['mc', 'mc2']}
                                    }),
                                "date_updated": scrapped_item["date_updated"],
                                "timestamp_updated": datetime.now(tz),
                            }
                        },
                    )
                )

                log_action(
                    "exam.detail",
                    "to_update",
                    code=code,
                    record=record,
                    scrapped_item=scrapped_item,
                    total_questions_count_in_qna=total_questions_count,
                )
                if vendor_code not in exams_to_update_or_create:
                    exams_to_update_or_create[vendor_code] = []
                exams_to_update_or_create[vendor_code].append((code, "update"))
                continue
            else:
                # just to make sure the rest unchanged exam details stored in db have the correct total_questions stored in qna database
                exam_detail_collection_operations.append(
                    UpdateOne(
                        {
                            "code": code,
                        },
                        {
                            "$set": {
                                "total_questions": total_questions_count,
                            }
                        },
                    )
                )
                total_questions_counts.append({code: total_questions_count})

                if (
                    (total_questions_count
                     < 0.9 * int(scrapped_item.get("claimed_total", 0))
                     or total_questions_count > int(scrapped_item.get("claimed_total", 0)))
                    # this is to handle the case where the total_questions_count is abnormally high
                    and code in popular_exams
                ):
                    # we use 90% as a threshold to ignore the site's newly added questions that are not yet in discussion list.
                    # focus on popular exams only
                    if vendor_code not in exams_to_update_or_create:
                        exams_to_update_or_create[vendor_code] = []
                    exams_to_update_or_create[vendor_code].append(
                        (code, "not_enough_questions")
                    )
                    continue

            # Check the additional conditions for date_last_complete_scan
            date_last_complete_scan = record.get("date_last_complete_scan")

            date_to_compare = datetime.now(tz)
            """ if record["date_updated"] is not None:
                date_to_compare = datetime.strptime(
                    record["date_updated"], "%Y-%m-%d"
                ).replace(tzinfo=tz)
                The date_updated is most probably faked by the server"""

            if (
                date_last_complete_scan is None
                or datetime.strptime(date_last_complete_scan, "%Y-%m-%d").replace(
                    tzinfo=tz
                )
                < date_to_compare
            ):
                continue
                # or (
                #    datetime.now(tz).date() - datetime.strptime
                # (date_last_complete_scan, "%Y-%m-%d").date() > timedelta(days=30)
                # )  # TODO
                # print(f"Period Update {code}: {record}")

                """ vendor_code = scrapped_item["vendor_code"]
                if vendor_code not in exams_to_update_or_create:
                    exams_to_update_or_create[vendor_code] = []
                exams_to_update_or_create[vendor_code].append(
                    (code, "period_update"))

                exam_detail_collection_operations.append(
                    UpdateOne(
                        {
                            "code": code,
                        },
                        {
                            "$set": {
                                # "vendor": vendor_id,
                                "vendor_code": scrapped_item["vendor_code"],
                                "name": scrapped_item["name"],
                                "is_popular": code in popular_exams,
                                "claimed_total": scrapped_item["claimed_total"],
                                "date_updated": scrapped_item["date_updated"],
                                "timestamp_updated": datetime.now(tz),
                            }
                        },
                    )
                )
                log_action(
                    "exam.detail",
                    "to_period_update",
                    code=code,
                    record=record,
                    scrapped_item=scrapped_item,
                ) """

        else:
            if vendor_code not in exams_to_update_or_create:
                exams_to_update_or_create[vendor_code] = []
            exams_to_update_or_create[vendor_code].append((code, "create"))

            exam_detail_collection_operations.append(
                InsertOne(
                    {
                        # "vendor": vendor_id,
                        "name": scrapped_item["name"],
                        "code": code,
                        "vendor_code": scrapped_item["vendor_code"],
                        "is_popular": code in popular_exams,
                        "claimed_total": scrapped_item["claimed_total"],
                        "date_updated": scrapped_item["date_updated"],
                        "total_questions": 0,
                        "date_last_complete_scan": None,
                        "timestamp": datetime.now(tz),
                    }
                )
            )

            log_action(
                "exam.detail",
                "to_create",
                code=code,
                # record=record,
                scrapped_item=scrapped_item,
            )
        record = []

    log_action(
        "exam.detail",
        "to_update_remaining_exam_details",
        total_questions_counts=total_questions_counts,
    )
    if exam_detail_collection_operations:
        exam_detail_collection.bulk_write(exam_detail_collection_operations)

    return exams_to_update_or_create

    # Output the list of unmatched items
    # print("Unmatched items in scrapped:", unmatched_items)


async def process_exam_details():
    # await bot_soup.initialize_session()
    discussion = link = x = None
    after_found = False
    after_found_code = False
    progress_steps = {
        "A": "updated_exam_catalogs",
        "B": "updated_exam_details",
        "C": "updated_qna",
    }
    log_action("scan", "start")
    try:
        state = load_state()

        timestamp_progress = state.get("timestamp_progress", {})
        popular_exams = decompress_data(state.get("popular_exams", []))
        exams_to_update_or_create = state.get("exams_to_update_or_create", {})
        scrapped_exam_details = decompress_data(
            state.get("scrapped_exam_details", []))
        scrapped_code = state.get("scrapped_code")

        timestamp_progress["started"] = datetime.now(tz)
        save_state(
            timestamp_progress=timestamp_progress,
        )

        # use intro.py to get all exam details
        providers = bot_soup.get_provider_names_and_slugs()
        provider_slugs = [provider_slug for _, provider_slug in providers]

        """ providers = random.sample(providers, k=5) if len(providers) >= 5 else providers
        print(providers) """

        vendor_operations = [
            UpdateOne(
                {"code": code},
                {"$set": {"name": name,
                          "timestamp_updated": datetime.now(tz)}},
                upsert=True,
            )
            for name, code in providers
        ]

        # Perform the batched upsert operation
        if vendor_operations != []:
            result = vendor_collection.bulk_write(vendor_operations)
            vendor_operations = []

        """ provider_slugs = random.sample(provider_slugs, k=3) if len(provider_slugs) >= 3 else provider_slugs
        print(provider_slugs) """

        """ A. Update Exam Catalog """
        if state is {} or not progress_steps["A"] in state.get(
            "timestamp_progress", {}
        ):

            all_exam_infos = await bot_soup.process_multiple_pages(
                provider_slugs,
                bot_soup.get_exam_info_new,
            )

            """ print(all_exam_infos) """
            popular_exams = [
                exam["exam_code"]
                for exam_list in all_exam_infos
                for exam in exam_list
                if exam["is_popular"]
            ]

            flattened_infos = [
                item["exam_code"] for sublist in all_exam_infos for item in sublist
            ]

            name_info_pairs = list(
                zip(
                    chain.from_iterable(
                        repeat(name, len(sublist))
                        for name, sublist in zip(provider_slugs, all_exam_infos)
                    ),
                    flattened_infos,
                )
            )

            scrapped_exam_details = await bot_soup.process_multiple_pages(
                name_info_pairs,
                bot_soup.get_exam_details_wrapper,
            )

        timestamp_progress[progress_steps["A"]] = datetime.now(tz)
        save_state(
            popular_exams=compress_data(popular_exams),
            scrapped_exam_details=compress_data(scrapped_exam_details),
            timestamp_progress=timestamp_progress,
        )

        gc.collect()
        """ B. Update Exam Details """
        if state is {} or not progress_steps["B"] in state.get(
            "timestamp_progress", {}
        ):
            if state is {} or state.get("scrapped_exam_details") is not None:
                scrapped_exam_details = decompress_data(
                    state.get("scrapped_exam_details")
                )

            exam_detail_records = list(exam_detail_collection.find())
            exams_to_update_or_create = compare_exam_lists_with_conditions(
                exam_detail_records, scrapped_exam_details, popular_exams
            )

        timestamp_progress[progress_steps["B"]] = datetime.now(tz)
        save_state(
            exams_to_update_or_create=exams_to_update_or_create,
            timestamp_progress=timestamp_progress,
        )

        gc.collect()

        """ C. Create/Update QNA """
        if state is {} or not progress_steps["C"] in state.get(
            "timestamp_progress", {}
        ):
            for provider_slug, exams in exams_to_update_or_create.items():
                # If it's not the first save, check the provider's state
                if not after_found and state.get("provider_slug") is not None:
                    if state.get("provider_slug") == provider_slug:
                        after_found = True
                    else:
                        print(
                            f"Skipping {provider_slug} discussion as it is already processed."
                        )
                        continue  # Skip this provider

                # If it's not the first save, check the provider's state
                if (
                    state.get("scrapped_discussions_result_provider_slug")
                    == provider_slug
                    and state.get("scrapped_discussions_result") is not None
                ):
                    discussions_result = decompress_data(
                        state.get("scrapped_discussions_result")
                    )
                    """ save_state(discussions_result=discussions_result)
                    exit() """
                    z = 0
                    """ for x, y in discussions_result.items():
                        print(x)
                        print(y)
                        if z == 3:
                            break
                        z += 1 """
                else:
                    print(f"Provider: {provider_slug}")

                    link = f"discussions/{provider_slug}/"
                    print(link)
                    # Get the total number of pages
                    LAST_PAGE = bot_soup.get_total_pages(link)
                    # LAST_PAGE = 1
                    if LAST_PAGE is None:
                        log_action("exception", "404", link=link)
                        continue
                    print(f"LAST_PAGE: {LAST_PAGE}")

                    x = 1

                    whole_discussions = await get_vendor_discussions(provider_slug, LAST_PAGE)
                    print("packing exam_codes...")
                    exam_codes = [exam[0] for exam in exams]

                    vendor_collection.update_one(
                        {
                            "code": provider_slug,
                        },
                        {
                            "$set": {
                                "discussion_page_total": LAST_PAGE,
                                "exam_total": len(exam_codes),
                                "timestamp_updated": datetime.now(tz),
                            }
                        },
                    )

                    discussions_result = {code: [] for code in exam_codes}

                    for link in whole_discussions:
                        # Normalize the link by replacing underscores with hyphens
                        normalized_link = link.replace("_", "-")
                        for code in exam_codes:
                            # Normalize the code by replacing underscores with hyphens
                            normalized_code = code.replace("_", "-")
                            # Match the normalized code as a whole word in the normalized link
                            if re.search(
                                r"(?<!\w)" +
                                re.escape(normalized_code) + r"(?!\w)",
                                normalized_link,
                                re.IGNORECASE,
                            ) or (code ==
                                  "aws-certified-machine-learning-engineer-associate-mla-c01" and
                                  "aws-certified-machine-learning-engineer-associate-mla" in link):
                                discussions_result[code].append(link)

                    save_state(
                        scrapped_discussions_result_provider_slug=provider_slug,
                        scrapped_discussions_result=compress_data(
                            discussions_result),
                    )

                """ log_action("discussions", "result", result=discussions_result) """
                x = 0
                z = 0
                qna_set = {}
                init_qna_set = {
                    # "to_create": 0,
                    "to_create_urls": [],
                    # "to_update": 0,
                    "to_update_urls": [],
                    "old_qnas": [],
                }
                for code, links in discussions_result.items():
                    """print(f"Links for exam {code}: {links}")
                    if z == 1:
                        exit()
                    z += 1"""
                    print(
                        f"{x} of {len(discussions_result)}: Checking {len(links)} scrapped discussion links from exam {code}"
                    )
                    qna_set[code] = init_qna_set.copy()
                    query = {"url": {"$in": links}}
                    qna_records = list(qna_db[provider_slug].find(query))
                    log_action(
                        "discussions",
                        "batch_query",
                        code=code,
                        qna_records=qna_records,
                    )
                    y = 0
                    if len(qna_records) == 0:
                        print(f"No DB records for exam {code}")
                    else:
                        qna_set[code]["to_update_urls"] = []
                        qna_set[code]["old_qnas"] = []
                        for qna_record in qna_records:
                            print(
                                f"Comparing with DB records: {y} of {len(qna_records)} ({code})"
                            )
                            timestamp_updated = qna_record.get(
                                "timestamp_updated")
                            timestamp = qna_record.get("timestamp")
                            timestamp_to_compare = timestamp_updated or timestamp

                            if timestamp_to_compare is None or (
                                datetime.now(tz).date() -
                                timestamp_to_compare.date()
                                > timedelta(days=30)
                            ):
                                # qna_set[code]["to_update"] += 1
                                qna_set[code]["to_update_urls"].append(
                                    qna_record["url"])
                                qna_set[code]["old_qnas"].append(qna_record)

                            y += 1
                    x += 1

                    """ print(links)
                    print(len(links))
                    print(len(qna_records))
                    print(set(links)) """
                    qna_record_links = [qna_record["url"]
                                        for qna_record in qna_records]
                    qna_set[code]["to_create_urls"] = list(
                        set(links) - set(qna_record_links)
                    )
                    # qna_set[code]["to_create"] = len(qna_set[code]["to_create_urls"])
                    """ save_state(
                        qna_set_code=code,
                        qna_set=qna_set[code],
                    )
                    set_links=set(links),
                    qna_record_links=set(qna_record_links),
                    to_create_urls=qna_set[code]["to_create_urls"] """

                    """ log_action(
                        "qna_set",
                        "loop test",
                        qna_set=qna_set,
                        init_qna_set=init_qna_set,
                    ) """
                    if qna_set[code] == init_qna_set:
                        del qna_set[code]

                    qna_records = []

                """ save_state(
                    qna_set_after_loop=qna_set,
                ) """

                if qna_set != {}:
                    log_action(
                        "qna_set",
                        "qna_set",
                        provider_slug=provider_slug,
                        qna_set=qna_set,
                    )

                gc.collect()
                log_memory_usage(
                    f"after scanning 1 whole discussion of {provider_slug} and after gc"
                )

                # update each exam detail for last scan date and total_questions
                qna_operations = []
                for code, items in qna_set.items():
                    if (
                        not after_found_code
                        and state is not {}
                        and scrapped_code is not None
                    ):
                        if scrapped_code == code:
                            after_found_code = True
                        print(f"Skipping {code} as it is already processed.")
                        continue  # Skip this provider
                    # Log the summary for the qna set
                    if len(items["to_create_urls"]) > 0:
                        log_action(
                            "qna_set",
                            "create",
                            provider_slug=provider_slug,
                            code=code,
                            # number_of_questions=items["to_create"],
                            urls=items["to_create_urls"],
                        )
                        print(f"Code to process: {code}")
                        pages_processed = await bot_soup.process_multiple_pages(
                            items["to_create_urls"]
                        )
                        x = 0
                        for to_create_url in items["to_create_urls"]:
                            if pages_processed[x] is not None:
                                qna_operations.append(
                                    InsertOne(
                                        {
                                            "url": to_create_url,
                                            "code": code,
                                            "timestamp": datetime.now(
                                                tz
                                            ),  # Use Hong Kong time
                                        }
                                        | pages_processed[x]
                                    )
                                )
                            else:
                                log_action(
                                    "exception",
                                    "to_create_url None",
                                    url=to_create_url,
                                    code=code,
                                )
                            x += 1

                    if len(items["to_update_urls"]) > 0:
                        log_action(
                            "qna_set",
                            "update",
                            provider_slug=provider_slug,
                            code=code,
                            old_qnas=items["old_qnas"],
                            # number_of_questions=items["to_update"],
                            urls=items["to_update_urls"],
                        )
                        pages_processed = await bot_soup.process_multiple_pages(
                            items["to_update_urls"]
                        )
                        x = 0
                        for to_update_url in items["to_update_urls"]:
                            if pages_processed[x] is not None:
                                qna_operations.append(
                                    UpdateOne(
                                        {"url": to_update_url},
                                        {
                                            "$set": {
                                                "timestamp_updated": datetime.now(tz),
                                                "code": code,
                                            }
                                            | pages_processed[x]
                                        },
                                    )
                                )
                            else:
                                log_action(
                                    "exception",
                                    "to_update_url None",
                                    url=to_update_url,
                                    code=code,
                                )
                            x += 1
                    if qna_operations:
                        qna_db[provider_slug].bulk_write(qna_operations)
                        print(
                            f"executed {len(qna_operations)} qna operations for {code}"
                        )
                        qna_operations = []

                    params = {
                        "date_last_complete_scan": datetime.now(tz).strftime(
                            "%Y-%m-%d"
                        ),
                        "total_questions": qna_db[provider_slug].count_documents({"code": {"$eq": code}}),
                        "timestamp_updated": datetime.now(tz),
                    }
                    original_exam_detail = exam_detail_collection.find_one_and_update(
                        {
                            "code": code,
                        },
                        {"$set": params},
                        return_document=ReturnDocument.BEFORE,
                    )
                    b4_q = original_exam_detail.get("total_questions", 0)
                    after_q = params.get("total_questions", 0)
                    claimed_total = original_exam_detail.get(
                        "claimed_total", 0)
                    b4_q_percent = 0
                    after_q_percent = 0

                    if claimed_total != 0:
                        b4_q_percent = math.ceil(b4_q / claimed_total * 100)
                        after_q_percent = math.ceil(
                            after_q / claimed_total * 100)

                    log_action(
                        "exam.detail",
                        "updated",
                        provider_slug=provider_slug,
                        code=code,
                        before=original_exam_detail,
                        after=params,
                        claimed_total=claimed_total,
                        b4_q=b4_q,
                        after_q=after_q,
                        b4_q_percent=b4_q_percent,
                        after_q_percent=after_q_percent,
                    )
                    # Save state after processing 1 whole provider discussion
                    save_state(provider_slug=provider_slug, scrapped_code=code)
                    gc.collect()

                """ log_memory_usage("after scanning 1 qna_set and before gc") """
                del qna_set
                gc.collect()
                log_memory_usage("after scanning 1 qna_set and after gc")

            del exams_to_update_or_create
            gc.collect()
        timestamp_progress[progress_steps["C"]] = datetime.now(tz)
        save_state(
            timestamp_progress=timestamp_progress,
        )

        log_action("scan", "end")  # Log the end of the scan
    except Exception as e:
        # Catch any exception and handle it
        error_type = type(e).__name__  # Get the exception type
        error_message = str(e)  # Get the exception message
        traceback_str = traceback.format_exc()
        print(f"An error occurred: {e}")
        log_action(
            "exception",
            error_type,
            message=error_message,
            traceback=traceback_str,
            url=discussion,
            discussions=f"{link}{x}",
        )
        log_action("scan", "end")  # Log the end of the scan

    """ finally:
        await bot_soup.session_close()  # Ensure the session is closed """


asyncio.run(process_exam_details())
