"""
Multi-platform exam subjects extractor.

This module extracts exam subjects/domains from certification exam guides
using platform-specific processors (PDF for AWS, web scraping for Azure).
"""

from datetime import datetime
import os
import sys
import argparse
from typing import List, Dict, Optional
from pymongo import MongoClient
import pytz

# Import platform configurations and processors
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from platforms.aws.config import AWS_CONFIG
from platforms.azure.config import AZURE_CONFIG
from platforms.aws.pdf_processor import process_aws_pdf, AWS_EXAM_PDF_URLS
from platforms.azure.web_scraper import process_azure_exam

# Define Hong Kong timezone
tz = pytz.timezone("Asia/Hong_Kong")

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
exam_db = client["exam"]
log_db = client["log"]
qna_db = client["qna"]

# Platform configurations
PLATFORM_CONFIGS = {
    'aws': {
        'config': AWS_CONFIG,
        'collection': 'amazon',
        'processor_type': 'pdf'
    },
    'azure': {
        'config': AZURE_CONFIG,
        'collection': 'microsoft',
        'processor_type': 'web_scraping'  # Future implementation
    }
}


def get_exam_codes_for_platform(platform: str) -> List[str]:
    """Get exam codes for a specific platform"""
    if platform not in PLATFORM_CONFIGS:
        print(f"🔥 Unknown platform: {platform}. Available: {list(PLATFORM_CONFIGS.keys())}")
        return []

    return PLATFORM_CONFIGS[platform]['config']['exam_codes']


def get_all_exam_codes() -> Dict[str, List[str]]:
    """Get exam codes for all platforms"""
    all_codes = {}
    for platform_name, platform_data in PLATFORM_CONFIGS.items():
        all_codes[platform_name] = platform_data['config']['exam_codes']
    return all_codes


def detect_platform_for_exam_code(exam_code: str) -> Optional[str]:
    """Detect which platform an exam code belongs to"""
    for platform_name, platform_data in PLATFORM_CONFIGS.items():
        if exam_code in platform_data['config']['exam_codes']:
            return platform_name
    return None


def process_aws_subjects(exam_codes: List[str] = None) -> None:
    """Process AWS exam subjects using PDF extraction"""
    if exam_codes is None:
        exam_codes = get_exam_codes_for_platform('aws')

    print(f"Processing AWS subjects for {len(exam_codes)} exams...")

    # For now, use the first PDF URL as a demonstration
    # In practice, you'd want to map specific exam codes to their PDF URLs
    pdf_url = AWS_EXAM_PDF_URLS[1]  # Cloud Practitioner as example

    print(f"Processing PDF: {pdf_url}")
    exam_code, domains = process_aws_pdf(pdf_url)

    if exam_code and domains:
        save_subjects_to_database(exam_code, domains)
    else:
        print("Failed to process AWS PDF")


def process_azure_subjects(exam_codes: List[str] = None) -> None:
    """Process Azure exam subjects using web scraping"""
    if exam_codes is None:
        exam_codes = get_exam_codes_for_platform('azure')

    print(f"Processing Azure subjects for {len(exam_codes)} exams...")

    # Process first 3 exams as demonstration (remove limit in production)
    for exam_code in exam_codes[:3]:
        print(f"\n🔄 Processing Azure exam: {exam_code}")

        # Use the Azure web scraper
        processed_code, domains = process_azure_exam(exam_code)

        if processed_code and domains:
            save_subjects_to_database(processed_code, domains)
        else:
            print(f"❌ Failed to process Azure exam: {exam_code}")

    if len(exam_codes) > 3:
        print(f"\n📝 Note: Only processed first 3 exams. {len(exam_codes) - 3} remaining exams can be processed when web scraping is fully implemented.")


def save_subjects_to_database(exam_code: str, domains: List[str]) -> None:
    """Save extracted subjects to database"""
    try:
        # Create subjects document
        subjects_doc = {
            "exam_code": exam_code,
            "domains": domains,
            "timestamp_created": datetime.now(tz),
            "timestamp_updated": datetime.now(tz)
        }

        # Insert or update in database
        exam_db["subjects"].update_one(
            {"exam_code": exam_code},
            {"$set": subjects_doc},
            upsert=True
        )

        print(f"Saved subjects for {exam_code}: {len(domains)} domains")

    except Exception as e:
        print(f"Error saving subjects to database: {e}")


def main():
    """Main function with CLI argument parsing"""
    parser = argparse.ArgumentParser(
        description="Multi-platform exam subjects extractor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all AWS exams (default for backward compatibility)
  python subjects.py

  # Process all AWS exams explicitly
  python subjects.py --platform aws

  # Process all Azure exams
  python subjects.py --platform azure

  # Process specific exam codes
  python subjects.py --exam_codes aws-certified-cloud-practitioner-clf-c02 az-104
        """
    )

    parser.add_argument("--platform", choices=['aws', 'azure'], default=None,
                        help="Certification platform to process")
    parser.add_argument("--exam_codes", nargs="+", default=None,
                        help="List of specific exam codes to process")

    args = parser.parse_args()

    print("🚀 Starting multi-platform exam subjects extraction")

    # Determine which exam codes to process
    if args.exam_codes:
        # Process specific exam codes
        exam_codes_to_process = args.exam_codes
        print(f"📋 Processing specific exam codes: {len(exam_codes_to_process)}")
        print(f"🔍 Exam codes: {', '.join(exam_codes_to_process)}")

        # Group exam codes by platform
        platform_groups = {}
        for code in exam_codes_to_process:
            platform = detect_platform_for_exam_code(code)
            if platform:
                if platform not in platform_groups:
                    platform_groups[platform] = []
                platform_groups[platform].append(code)
                print(f"🎯 {code} -> {platform} platform")
            else:
                print(f"⚠️ Could not determine platform for exam code: {code}")

        # Process each platform group
        for platform, codes in platform_groups.items():
            print(f"\n🔥 Processing {platform.upper()} platform ({len(codes)} exams)")
            if platform == 'aws':
                process_aws_subjects(codes)
            elif platform == 'azure':
                process_azure_subjects(codes)

    elif args.platform:
        # Process all exams for specific platform
        print(f"📋 Processing all {args.platform.upper()} exams")

        if args.platform == 'aws':
            process_aws_subjects()
        elif args.platform == 'azure':
            process_azure_subjects()

    else:
        # Default behavior for backward compatibility - process AWS
        print("📋 No platform specified, defaulting to AWS (backward compatibility)")
        process_aws_subjects()

    print("\n🎉 Exam subjects extraction completed!")


if __name__ == "__main__":
    main()