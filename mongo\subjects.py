from datetime import datetime
import os
import requests
import random
import time
import pdfplumber
from pymongo import MongoClient
import re
import sys
from typing import List, Dict, Optional
from motor.motor_asyncio import AsyncIOMotorClient
from typing import List, Optional, Dict
from pymongo import MongoClient, ReturnDocument, UpdateOne, InsertOne
import pytz
import tiktoken
import json

# Define Hong Kong timezone
tz = pytz.timezone("Asia/Hong_Kong")

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
exam_db = client["exam"]
log_db = client["log"]
qna_db = client["qna"]

# Step 1: Download the PDF


def download_pdf(url, output_file="downloaded.pdf"):
    try:
        # 打印开始下载的信息
        print("Downloading PDF...")
        # 发送HTTP GET请求
        response = requests.get(url)
        # 检查HTTP响应状态，如果有问题则抛出异常
        response.raise_for_status()  # Raise an error for HTTP issues
        # 打开文件以二进制写入模式
        with open(output_file, "wb") as f:
            # 将响应内容写入文件
            f.write(response.content)
        # 打印下载成功的信息
        print(f"PDF downloaded successfully and saved as {output_file}")
    except Exception as e:
        # 捕获异常并打印错误信息
        print(f"Error downloading PDF: {e}")

# Step 2: Extract Text from the PDF


def extract_text(pdf_file="downloaded.pdf"):
    try:
        print("Extracting text from PDF...")
        all_text = ""
        with pdfplumber.open(pdf_file) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:  # Ensure the page contains text
                    all_text += text + " "  # Add a space between pages
        if not all_text.strip():
            print("Warning: No text was extracted from the PDF.")
        return all_text.strip()
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None

# Step 3: Clean the Extracted Text


def clean_text(text: str) -> str:
    """
    Cleans the input text by removing all strings before the first occurrence of "Domain 1"
    and all strings after the last occurrence of "Appendix".

    Args:
        text (str): The input text to clean.

    Returns:
        str: The cleaned text.
    """
    try:
        start_keyword = "Domain 1"
        end_keyword = "Appendix"

        # Find the first occurrence of "Domain 1"
        start_index = text.find(start_keyword)
        if start_index == -1:
            raise ValueError(
                f"Start keyword '{start_keyword}' not found in the text.")

        # Find the last occurrence of "Appendix"
        end_index = text.rfind(end_keyword)
        if end_index == -1:
            raise ValueError(
                f"End keyword '{end_keyword}' not found in the text.")

        # Extract the relevant portion of the text
        cleaned_text = text[start_index:end_index +
                            len(end_keyword)].replace('\n', ' ').replace('"', r'\"').strip()

        # Save the cleaned text to a file for debugging
        with open("cleaned_text_debug.txt", "w", encoding="utf-8") as f:
            f.write(cleaned_text)
        print("Cleaned text saved to 'cleaned_text_debug.txt' for debugging.")

        cleaned_text = send_to_gpt(
            cleaned_text,
            "identify the specific AWS certification exam code from this text. Then provide a structured summary of each domain and its task statements using the exact numbering format (Domain 1, Task 1.1, etc.). Include all key technical requirements, knowledge areas, and skills mentioned. Be comprehensive but concise."
        ).replace('\n', ' ').replace('"', r'\"').strip()

        # Save the cleaned text to a file for debugging
        with open("cleaned_text_debug_after_ai_summary.txt", "w", encoding="utf-8") as f:
            f.write(cleaned_text)
        print("Cleaned text after ai summary saved to 'cleaned_text_debug_after_ai_summary.txt' for debugging.")

        return cleaned_text
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        if e:
            print(f"Response status code: {e.status_code}")
            print(f"Response content: {e.content}")
    except Exception as e:
        print(f"Error cleaning text: {e}")
        return text


def send_to_gpt(
    text: str,
    prompt: str,
    bearer_tokens: List[str] = [
        "sk-yOGPPghWJXYZs4mQ9hP4XKPz5EoSHZPCxH9kQk3W05G2JK1F",
        "sk-lmab8Wl6AVU3VLKjsoGTP4H3zEK1CSV9HE1H7W7eOq3HXFPi"
    ],
    model: str = "gpt-4.1-mini",
    max_retries: int = 30,
    save_folder: str = "ai_responses"
) -> Optional[Dict]:
    # Load existing counters
    api_counter_file = "api_counter.txt"
    if os.path.exists(api_counter_file):
        with open(api_counter_file, "r") as f:
            api_counters = json.load(f)
    else:
        api_counters = {}

    # Get current date
    today = datetime.now().strftime("%Y-%m-%d")

    # Initialize counters for today if not present
    if today not in api_counters:
        api_counters[today] = {}

    error_data = {
        "system_prompt": text,
        "user_prompt": prompt,
        "model": model,
        "errors": []
    }

    json_payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": text},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7
    }

    if not bearer_tokens:
        print("🚨 Critical: No valid API tokens provided", file=sys.stderr)
        sys.exit(1)

    active_tokens = set(bearer_tokens)
    disabled_tokens = set()
    last_working_token = None

    for attempt in range(max_retries + 1):
        if not active_tokens:
            print("🔥 Catastrophic: All API tokens exhausted", file=sys.stderr)
            _print_debug_info(error_data)
            _save_error_payload(json_payload)
            sys.exit(1)

        # Smart token selection with fallback
        current_token = (
            last_working_token if last_working_token and last_working_token in active_tokens
            else random.choice(list(active_tokens))
        )
        token_suffix = current_token[-6:]

        response = None  # Initialize response to None
        try:
            response = requests.post(
                "https://api.chatanywhere.tech/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {current_token}",
                    "Content-Type": "application/json"
                },
                json=json_payload,
                timeout=60
            )
            response.raise_for_status()
            content = response.json().get("choices", [{}])[0].get("message", {}).get("content", "")
            if not content:
                print("⚠️ Warning: Empty response content")
                return None

            # Save the response to a text file
            os.makedirs(save_folder, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(save_folder, f"ai_response_{timestamp}.txt")
            with open(filename, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"AI response saved to '{filename}'")

            # Update last working token
            last_working_token = current_token

            # Update API counter
            ip_address = requests.get('https://api.ipify.org').text
            if current_token not in api_counters[today]:
                api_counters[today][current_token] = {"count": 0, "ip": ip_address}
            api_counters[today][current_token]["count"] += 1
            api_counters[today][current_token]["ip"] = ip_address

            # Save updated counters
            with open(api_counter_file, "w") as f:
                json.dump(api_counters, f, indent=2)

            return content

        except requests.exceptions.RequestException as e:
            # Initialize status_code and response_text
            status_code = None
            response_text = 'No response'

            if response:
                try:
                    print(f"Response object: {response}")
                    status_code = response.status_code
                    response_text = response.text
                    print(f"Response status code: {status_code}")
                    print(f"Response text: {response_text[:200]}")
                except Exception as err:
                    print(f"Error accessing response attributes: {err}")
            else:
                print("Response is None")
                # Extract status code from exception message if possible
                if '429' in str(e):
                    status_code = 429
                elif '401' in str(e):
                    status_code = 401
                elif '403' in str(e):
                    status_code = 403

            # Track error details
            error_entry = {
                "attempt": attempt + 1,
                "token_suffix": token_suffix,
                "status": status_code,
                "error": str(e),
                "response": response_text[:500] + "..." if len(response_text) > 500 else response_text
            }
            print(f"⚠️ response: {response}")
            print(f"⚠️ error_entry: {error_entry}")
            error_data["errors"].append(error_entry)

            # Token failure analysis
            if status_code in [401, 403]:
                print(f"🔒 Authentication Failure (Code {status_code}) - Disabling token ...{token_suffix}")
                disabled_tokens.add(current_token)
                active_tokens.discard(current_token)
            elif status_code == 429:
                print(f"⏰ Rate Limit Hit - Disabling token ...{token_suffix}")
                disabled_tokens.add(current_token)
                active_tokens.discard(current_token)
            else:
                print(f"❌ Unexpected Error (Code {status_code}) - Retaining token ...{token_suffix}")

            # Final failure handling
            if attempt >= max_retries:
                print("\n🔥 Maximum retries exhausted. Error summary:", file=sys.stderr)
                _print_debug_info(error_data)
                sys.exit(1)

            # Adaptive backoff with jitter
            sleep_time = random.uniform(0.5, 1.5) * (2 ** min(attempt//2, 5))
            print(f"⏱️  Retrying in {sleep_time:.1f}s (Active tokens: {len(active_tokens)})")
            time.sleep(sleep_time)

    return None


def _print_debug_info(error_data: dict):
    """Print structured error information for diagnostics"""
    print("\n=== ERROR DIAGNOSTICS ===")
    print(f"Model: {error_data['model']}")
    print(f"System Prompt: {error_data['system_prompt'][:200]}...")
    print(f"User Prompt: {error_data['user_prompt'][:200]}...")
    print("\nError Timeline:")
    for error in error_data["errors"]:
        print(f"Attempt {error['attempt']}:")
        print(f"  Token: ...{error['token_suffix']}")
        print(f"  Status: {error['status']}")
        print(f"  Error: {error['error']}")
        print(f"  Response: {error['response'][:200]}...")
    print("========================")


def _save_error_payload(payload: dict):
    """Save JSON payload to timestamped file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"gpt_error_payload_{timestamp}.json"

    try:
        with open(filename, 'w') as f:
            json.dump(payload, f, indent=2)
        print(f"Saved error payload to {filename}")
    except Exception as e:
        print(f"Failed to save error payload: {str(e)}")


def get_exam_code_and_subjects_from_gpt(gpt_response):
    """
    Extracts exam code and domains from GPT response in format "[exam code]|domain 1|domain 2"

    Args:
        gpt_response (dict): Response from GPT API

    Returns:
        tuple: (exam_code: str, domains: list) or (None, []) if extraction fails
    """
    try:
        # Split and clean parts
        parts = [part.strip() for part in gpt_response.split("|")]

        # Validate format
        if len(parts) < 2:
            print(
                f"Error: Invalid format in GPT response. Expected 'code|domain1|domain2', got: '{gpt_response}'")
            return None, []

        exam_code = parts[0]
        domains = parts[1:]

        # Validate exam code is not empty
        if not exam_code:
            print("Error: Empty exam code in GPT response")
            return None, []

        # Validate at least one domain exists
        if not domains:
            print("Error: No domains found in GPT response")
            return None, []

        return exam_code, domains

    except Exception as e:
        print(f"Error extracting exam code and domains: {e}")
        return None

# Step 5: Query MongoDB and Validate Data


def get_log_subject(exam_code):
    try:
        log_subject = log_db["subjects"].find_one(
            {"code": {"$regex": exam_code, "$options": "i"}},
            sort=[("timestamp", -1)]  # Get most recent document
        )
        if log_subject:
            # print(f"Log subject found: {log_subject}")
            print(f"Log subject found")
            return log_subject
        else:
            print(f"No log subject found for {exam_code}")
            return None
    except Exception as e:
        print(f"Error retrieving log subject: {e}")
        return None


def validate_exam_details(exam_code, gpt_domains):
    try:
        exam_detail = exam_db["detail"].find_one({
            "$or": [
                {"code": {"$regex": exam_code, "$options": "i"}},
                {"name": {"$regex": exam_code, "$options": "i"}}
            ]
        })

        if exam_detail:
            db_exam_code = exam_detail.get('exam_code') or []
            if db_exam_code != exam_code:
                print(f"Upserting valid/updated exam code {exam_code} to db")
                exam_detail = exam_db["detail"].find_one_and_update(
                    {
                        "$or": [
                            {"code": {"$regex": exam_code, "$options": "i"}},
                            {"name": {"$regex": exam_code, "$options": "i"}}
                        ]
                    },
                    {"$set": {"exam_code": exam_code}},
                    return_document=ReturnDocument.AFTER
                )

            exam_detail_subjects = exam_detail.get("subjects") or []
            if exam_detail_subjects == gpt_domains:
                print("Subjects match with db - no comparison needed")
                return True

        return False

    except Exception as e:
        print(f"Validation error: {e}")
        return False, []


def update_exam_detail_and_qna_domains_with_gpt_domains(exam_code, gpt_domains):
    try:
        # First check if log subject exists
        log_subject = get_log_subject(exam_code)
        if not log_subject:
            print(f"No log subject found for {exam_code} - aborting update")
            return False

        # Proceed with updates since log exists
        exam_detail = exam_db["detail"].find_one(
            {
                "$or": [
                    {"code": {"$regex": exam_code, "$options": "i"}},
                    {"name": {"$regex": exam_code, "$options": "i"}}
                ]
            })
        if not exam_detail:
            print(f"No exam details found for {exam_code}")
            return False

        qna_collection_operations = []

        print(
            f"Updating {exam_code} domains from log_subject to qna_collection...")

        # Process questions from the verified log subject
        for q_idx, question in enumerate(log_subject.get("questions", []), 1):
            try:
                url = question.get("url")
                domain = question.get("domain")

                if not url or not domain:
                    print(f"Skipping malformed question {q_idx}")
                    continue

                # Create update operation for Q&A collection
                qna_collection_operations.append(
                    UpdateOne(
                        {
                            "url": url,
                            "code": exam_detail["code"]
                        },
                        {
                            "$set": {
                                "subject": domain,
                                "timestamp_updated": datetime.now(tz)
                            }
                        },
                    )
                )

            except Exception as e:
                print(f"Error processing question {q_idx}: {str(e)}")
                continue

        # Bulk write if we have operations
        if qna_collection_operations:
            qna_db["amazon"].bulk_write(qna_collection_operations)

        # Update exam detail with GPT domains
        exam_db["detail"].update_one(
            {"_id": exam_detail["_id"]},
            {
                "$set": {
                    "subjects": gpt_domains,
                    "total_questions": len(log_subject.get("questions", [])),
                    "total_questions_mc_or_mc2": qna_db["amazon"].count_documents({
                        "code": exam_detail["code"],
                        "subject": {"$exists": True},
                        "type": {"$in": ['mc', 'mc2']} # at the moment we just care about mc and mc2
                    }),
                    "timestamp_subjects_updated": datetime.now(tz)
                }
            }
        )

        print(
            f"Successfully updated both Q&A collection and exam details for {exam_code}")

        """ # Check for extra question URLs in log_subject
        log_question_urls = {q["url"]
            for q in log_subject.get("questions", [])}
        qna_questions = list(qna_db["amazon"].find({
            "code": exam_detail["code"],
            "subject": {"$exists": True}
        }))
        qna_question_urls = {q["url"] for q in qna_questions}

        extra_urls = log_question_urls - qna_question_urls
        if extra_urls:
            print(f"Found {len(extra_urls)} extra URLs in log_subject")
            extra_urls_file = f"extra_urls_{exam_code}.txt"
            with open(extra_urls_file, "w") as f:
                f.write("\n".join(sorted(extra_urls)))
            print(f"Extra URLs saved to {extra_urls_file}") """

        return True

    except Exception as e:
        print(f"Error updating exam detail and Q&A domains: {e}")
        return False

# Helper Function: Get Filtered Questions (modified for batch processing)


def get_filtered_questions(qna_collection, exam_code, classified_q):
    try:
        exam_detail = exam_db["detail"].find_one(
            {
                "$or": [
                    {"code": {"$regex": exam_code, "$options": "i"}},
                    {"name": {"$regex": exam_code, "$options": "i"}}
                ]
            })
        if not exam_detail:
            print(
                f"No exam details found for {exam_code}. Cannot get filtered questions.")
            return False

        print(f"Code in exam detail: {exam_detail.get('code')}")

        classified_urls = [item["url"] for item in classified_q]
        # print(f"list of classified urls: ", classified_urls)

        query = {"code": exam_detail.get('code')}
        if classified_urls:
            query["url"] = {"$nin": classified_urls}

        #print(f"query:", query)

        result_list = list(qna_collection.find(query).sort("_id", 1).limit(20))
        filtered_urls = [item["url"] for item in result_list]
        # print(f"list of filtered urls: ", filtered_urls)
        return result_list
    except Exception as e:
        print(f"Error retrieving filtered questions: {e}")
        return []


# Step 5: Synchronous MongoDB Operations


def insert_log_subject_if_domains_differ(gpt_domains, exam_code):
    try:
        gpt_domains = [d.strip()
                       for d in gpt_domains if d.strip()]
        collection = log_db["subjects"]

        # Find the latest existing document with timestamp
        existing = get_log_subject(exam_code)

        needs_insert = False

        if existing:
            print(f"Existing domains: {existing.get('domains', [])}")
            existing_domains = [d
                                for d in existing.get("domains", []) if d]

            # Check if domains are null or different
            if not existing_domains or set(existing_domains) != set(gpt_domains):
                needs_insert = True
                print("🔄 Domains differ or null - creating new record")
        else:
            needs_insert = True
            print("🆕 No existing document - creating new")

        if needs_insert:
            new_document = {
                "code": exam_code.upper(),
                "domains": gpt_domains,
                "questions": [],
                "timestamp": datetime.now(tz)
            }
            result = collection.insert_one(new_document)
            return result.inserted_id is not None

        print("✅ Domains match - no action needed")
        return False

    except Exception as e:
        print(f"Error updating log: {str(e)}")
        return False

# Main Function


def main():
    pdf_urls = [
        "https://d1.awsstatic.com/training-and-certification/docs-ai-practitioner/AWS-Certified-AI-Practitioner_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-cloud-practitioner/AWS-Certified-Cloud-Practitioner_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-sa-pro/AWS-Certified-Solutions-Architect-Professional_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-devops-pro/AWS-Certified-DevOps-Engineer-Professional_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-sysops-associate/AWS-Certified-SysOps-Administrator-Associate_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-sa-assoc/AWS-Certified-Solutions-Architect-Associate_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-data-engineer-associate/AWS-Certified-Data-Engineer-Associate_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-advnetworking-spec/AWS-Certified-Advanced-Networking-Specialty_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-machine-learning-engineer-associate/AWS-Certified-Machine-Learning-Engineer-Associate_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-ml/AWS-Certified-Machine-Learning-Specialty_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-security-spec/AWS-Certified-Security-Specialty_Exam-Guide.pdf",
        "https://d1.awsstatic.com/training-and-certification/docs-dev-associate/AWS-Certified-Developer-Associate_Exam-Guide.pdf"
    ]
    pdf_urls = [
        "https://d1.awsstatic.com/training-and-certification/docs-cloud-practitioner/AWS-Certified-Cloud-Practitioner_Exam-Guide.pdf",
    ]

    for url in pdf_urls:
        download_pdf(url)
        # Extract and clean text
        extracted_text = extract_text("downloaded.pdf")
        if not extracted_text:
            print("Error: No text extracted")
            return

        # Check if text was successfully extracted
        cleaned_text = clean_text(extracted_text)
        if not cleaned_text:
            print("Error: Cleaned text empty")
            return

        # Send the cleaned text to GPT and get the response
        gpt_response = send_to_gpt(
            cleaned_text, "Please state exam code, exam name and the domains in the following format: [exam code]|[domain name 1]|[domain name 2]| etc")
        if not gpt_response:
            print("Error: No GPT response")
            return

        print(gpt_response)

        exam_code, gpt_domains = get_exam_code_and_subjects_from_gpt(
            gpt_response)
        """ print("exam code: ", exam_code) """
        print("gpt domains: ", gpt_domains)

        """ # Validate the GPT response against MongoDB
        if validate_exam_details(exam_code, gpt_domains):
            update_exam_detail_and_qna_domains_with_gpt_domains(
                exam_code, gpt_domains)
            continue """

        update_result = insert_log_subject_if_domains_differ(
            gpt_domains, exam_code)
        print(f"Update log_subject result: {update_result}")

        # Check if GPT response is valid
        total_processed = 0
        processed_count = 1
        while True:

            classified_q = []
            log_subject = get_log_subject(exam_code)
            if log_subject and "questions" in log_subject:
                classified_q = log_subject["questions"]
                print(f"Classified questions: {len(classified_q)}")

            qna_questions = get_filtered_questions(
                qna_db["amazon"], exam_code, classified_q)
            if not qna_questions:
                print("No more questions to process.")
                break

            print("\nFiltered questions: ", len(qna_questions))
            # Process current batch
            print(f"\nProcessing batch {processed_count}")

            filtered_q = []
            for i, q in enumerate(qna_questions):
                try:
                    # Format question with choices
                    question_text = q['exam']['question'].replace(
                        '\n', ' ').strip()
                    choices_str = ""
                    if 'choices' in q['exam'] and isinstance(q['exam']['choices'], list):
                        choices = []
                        for choice_dict in q['exam']['choices']:
                            for letter, text in choice_dict.items():
                                cleaned_choice = text.replace(
                                    '\n', ' ').strip()
                                choices.append(
                                    f"{letter}: {cleaned_choice}")
                        choices_str = "Choices: " + ", ".join(choices)

                    filtered_q.append(
                        f"Question {total_processed + i + 1}: {question_text}? {choices_str}")
                except KeyError:
                    print(
                        f"Warning: Skipping malformed question {q.get('_id', 'unknown')}")
                    continue

            # Process questions with GPT
            encoder = tiktoken.encoding_for_model("gpt-4o-mini")

            cleaned_text_with_q = cleaned_text
            # current_length = len(cleaned_text_with_q)
            current_tokens = len(encoder.encode(cleaned_text_with_q))

            print(f"Initial token for cleaned_text: {current_tokens}")
            # print(f"Initial length for cleaned_text: {current_length}")
            processed_in_batch = 0
            x = 0
            print(f"length for question: {len(filtered_q)}")

            for question in filtered_q:
                print(f"{processed_in_batch}: {qna_questions[x]['url']}")
                cleaned_text_with_q_tokens = len(
                    encoder.encode(cleaned_text_with_q + question))
                print(
                    f"cleaned_text_with_q_tokens: {cleaned_text_with_q_tokens}")
                # print("question", question)
                # print("question_tokens", question_tokens)
                # if current_length + len(question) > 19000:
                if cleaned_text_with_q_tokens > 3800:
                    print(
                        f"Token limit reached. Processed {processed_in_batch} in this batch.")
                    #    f"Character limit reached. Processed {processed_in_batch} in this batch.")
                    break

                cleaned_text_with_q += question

                # current_length += len(question)
                # print(f"length for question: {len(question)}")
                # print(f"question: {question}")
                # print(f"current_length: {current_length}")
                processed_in_batch += 1
                x += 1

            try:
                with open("cleaned_text_with_questions.txt", "w", encoding="utf-8") as f:
                    f.write(cleaned_text_with_q)
                print(
                    "Saved cleaned text with questions to 'cleaned_text_with_questions.txt'")
            except Exception as e:
                print(f"\nError saving to text file: {e}")

            # Send to GPT and update log
            if processed_in_batch == 0:
                print("No questions to process. Exiting.")
                break

            q_subject_response = send_to_gpt(
                cleaned_text_with_q,
                "What domain and task statement should the questions belong to? Please put this in the following format: Q1: Domain X1, Task Z1\nQ2: Domain X2, Task X2\n etc., whereas Q1 represents Question 1, and Task Z1 represents Q1's task statement, etc. Please do not include any other text or explanation."
            )

            if not q_subject_response:
                print("Error: No GPT response for questions")
                continue

            # Update log_db with processed questions
            try:
                print("GPT response for questions: ", q_subject_response)

                log_doc = get_log_subject(exam_code)

                z = 0
                for line in q_subject_response.split('\n'):
                    if not line.strip():
                        continue

                    try:
                        # Improved parsing with regex
                        match = re.match(
                            r'.*Q(\d+):?\s*Domain\s*(\d+),?\s*Task\s*(?:Statement\s*)?([\d.]+)', line, re.IGNORECASE)
                        if not match:
                            print(
                                f"Skipping unparseable line: {line}")
                            continue

                        q_num = int(match.group(1))
                        domain = match.group(2)
                        task = float(match.group(3))

                        question = qna_questions[z]
                        if 'url' not in question:
                            print(
                                f"Warning: URL not found in question {q_num}")

                        log_entry = {
                            "url": question['url'],
                            "domain": domain,
                            "task": task
                        }
                        # Check for existing entry
                        if not any(q.get('url') == log_entry['url'] for q in log_doc.get("questions", [])):
                            log_doc.setdefault(
                                "questions", []).append(log_entry)
                            """ print(
                                f"Added Q{q_num}: {log_entry}") """
                        else:
                            print(
                                f"Skipping duplicate URL: {log_entry['url']}")
                        z += 1
                    except (ValueError, IndexError, KeyError) as e:
                        print(
                            f"Error processing line '{line}': {str(e)}")
                        continue

                # Update database
                if '_id' in log_doc:
                    log_db["subjects"].update_one(
                        {"_id": log_doc["_id"]},
                        {"$set":
                            {
                                "questions": log_doc.get("questions", []),
                                "timestamp": datetime.now(tz)
                            }
                         },
                        upsert=True
                    )

                print(
                    f"Updated log with {len(log_doc.get('questions', []))} total questions")
                total_processed += processed_in_batch
                processed_count += 1
            except Exception as e:
                print(f"Error updating log: {e}")

        update_exam_detail_and_qna_domains_with_gpt_domains(
            exam_code, gpt_domains)


if __name__ == "__main__":
    main()
