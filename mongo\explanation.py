from datetime import datetime
from pymongo import MongoClient, UpdateOne
import pytz
import requests
import random
import time
import json
import sys
import tiktoken
import os
import argparse
from typing import Callable, Optional, List, Dict, Tuple

# Import platform configurations
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from platforms.aws.config import AWS_CONFIG
from platforms.azure.config import AZURE_CONFIG

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
qna_db = client["qna"]
exam_db = client["exam"]
tz = pytz.timezone("Asia/Hong_Kong")

# Configuration
TOKEN_LIMIT = 3800
ENCODER = tiktoken.encoding_for_model("gpt-4o-mini")

# Platform configurations
PLATFORM_CONFIGS = {
    'aws': {
        'config': AWS_CONFIG,
        'collection': 'amazon'
    },
    'azure': {
        'config': AZURE_CONFIG,
        'collection': 'microsoft'
    }
}


def get_exam_codes_for_platform(platform: str) -> List[str]:
    """Get exam codes for a specific platform"""
    if platform not in PLATFORM_CONFIGS:
        print(f"🔥 Unknown platform: {platform}. Available: {list(PLATFORM_CONFIGS.keys())}")
        return []

    return PLATFORM_CONFIGS[platform]['config']['exam_codes']


def get_all_exam_codes() -> Dict[str, List[str]]:
    """Get exam codes for all platforms"""
    all_codes = {}
    for platform_name, platform_data in PLATFORM_CONFIGS.items():
        all_codes[platform_name] = platform_data['config']['exam_codes']
    return all_codes


def send_to_gpt(
    text: str,
    prompt: str,
    bearer_tokens: List[str] = [
        "sk-yOGPPghWJXYZs4mQ9hP4XKPz5EoSHZPCxH9kQk3W05G2JK1F",
        "sk-lmab8Wl6AVU3VLKjsoGTP4H3zEK1CSV9HE1H7W7eOq3HXFPi"
    ],
    model: str = "gpt-4.1-mini",
    max_retries: int = 30
) -> Optional[str]:
    """Enhanced GPT API request with token management"""
    error_data = {
        "system_prompt": text,
        "user_prompt": prompt,
        "model": model,
        "errors": []
    }

    json_payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": text},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7
    }

    if not bearer_tokens:
        print("🚨 Critical: No valid API tokens provided", file=sys.stderr)
        sys.exit(1)

    active_tokens = set(bearer_tokens)
    disabled_tokens = set()
    last_working_token = None

    for attempt in range(max_retries + 1):
        if not active_tokens:
            print("🔥 All API tokens exhausted", file=sys.stderr)
            sys.exit(1)

        current_token = (
            last_working_token
            if last_working_token and last_working_token in active_tokens
            else random.choice(list(active_tokens))
        )
        token_suffix = current_token[-6:]

        try:
            response = requests.post(
                "https://api.chatanywhere.tech/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {current_token}",
                    "Content-Type": "application/json"
                },
                json=json_payload,
                timeout=45
            )
            response.raise_for_status()

            content = response.json().get("choices", [{}])[
                0].get("message", {}).get("content", "")
            return content if content else None

        except requests.exceptions.RequestException as e:
            status_code = getattr(response, 'status_code', None)
            response_text = getattr(response, 'text', 'No response')

            error_entry = {
                "attempt": attempt + 1,
                "token_suffix": token_suffix,
                "status": status_code,
                "error": str(e),
                "response": response_text[:500] + "..." if len(response_text) > 500 else response_text
            }
            error_data["errors"].append(error_entry)

            if status_code in [401, 403]:
                disabled_tokens.add(current_token)
                active_tokens.discard(current_token)
            elif status_code == 429:
                if "free account is limited" in response_text:
                    disabled_tokens.add(current_token)
                    active_tokens.discard(current_token)

            if attempt >= max_retries:
                print("🔥 Maximum retries exhausted")
                sys.exit(1)

            sleep_time = random.uniform(0.5, 1.5) * (2 ** min(attempt//2, 5))
            time.sleep(sleep_time)

    return None


def format_batch_prompt(questions: List[Dict]) -> str:
    """Format a batch of questions for GPT processing with enhanced validation"""
    batch_prompt = ""
    for q_idx, question in enumerate(questions, 1):
        try:
            q_data = question['exam']
            prompt = f"## Question {q_idx}:\n{q_data['question']}\n"

            # Validate choices format
            if not isinstance(q_data['choices'], list):
                raise ValueError("Choices is not a list")

            for choice in q_data['choices']:
                if not isinstance(choice, dict):
                    raise ValueError("Choice is not a dictionary")
                for letter, text in choice.items():
                    prompt += f"{letter}: {text}\n"

            # Validate answers
            if not isinstance(q_data['answer'], list):
                raise ValueError("Answers is not a list")

            prompt += f"\nCorrect Answer(s): {', '.join(q_data['answer'])}\n\n"
            batch_prompt += prompt

        except (KeyError, ValueError) as e:
            print(f"🛑 Skipping question {question.get('url')} - {str(e)}")
            continue

    return batch_prompt


def process_exam_questions(exam_code: str, platform: str = "aws"):
    """Process all questions using improved token-aware batching"""
    if platform not in PLATFORM_CONFIGS:
        print(f"🔥 Unknown platform: {platform}")
        return

    collection_name = PLATFORM_CONFIGS[platform]['collection']
    collection = qna_db[collection_name]
    query = {
        "code": exam_code,
        "exam.ai_explanation": {"$exists": False},
        "$or": [
            {"ai_explanation": {"$exists": False}},
            {"ai_explanation": ""}
        ],
        "exam.choices": {"$exists": True, "$ne": []},
        "exam.answer": {"$exists": True, "$ne": []},
        "type": {"$in": ["mc", "mc2"]}
    }

    print(f"\n{'='*40}\nProcessing: {exam_code}\n{'='*40}")

    try:
        # Get total count for progress tracking
        total_questions = collection.count_documents(query)
        print(f"Found {total_questions} questions needing explanations")

        # Retrieve and validate all questions first
        valid_questions = []
        processed_count = 0
        cursor = collection.find(query).batch_size(100)

        for question in cursor:
            try:
                processed_count += 1
                if processed_count % 50 == 0:
                    print(
                        f"⏳ Validated {processed_count}/{total_questions} questions...")

                # Validate question structure
                qexam = question.get('exam', {})
                if not all(key in qexam for key in ['choices', 'answer']):
                    print(
                        f"⚠️ Skipping malformed question: {question.get('url', 'No URL')}")
                    continue

                valid_questions.append(question)

            except Exception as e:
                print(
                    f"🛑 Error validating question {question.get('url')}: {str(e)}")
                continue

        print(
            f"✅ Validated {len(valid_questions)}/{total_questions} questions")

        # Configure batch processing
        base_prompt = ""

        def batch_processor(batch: List[Dict]):
            """Closure to handle batch processing with context"""
            process_batch(batch, exam_code, collection)

        # Process using reusable token-aware batcher
        process_in_batches(
            questions=valid_questions,
            base_prompt=base_prompt,
            process_fn=batch_processor,
            token_limit=TOKEN_LIMIT
        )

        print(
            f"✅ Completed {exam_code}: Processed {len(valid_questions)} explanations")
        if total_questions > len(valid_questions):
            print(
                f"⚠️  {total_questions - len(valid_questions)} questions skipped due to errors")

    except Exception as e:
        print(f"🔥 Critical error processing {exam_code}: {str(e)}")
        raise


def process_batch(batch: List[Dict], exam_code: str, collection, is_reprocess: bool = False):
    """Process a single batch of questions with logging"""
    # Create explanations directory if not exists
    os.makedirs('explanations', exist_ok=True)
    log_entries = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = os.path.join(
        # Modified path
        "explanations", f"explanations_log_{exam_code}_{timestamp}.txt")

    try:
        batch_prompt = format_batch_prompt(batch)
        explanation_prompt = (
            "IMPORTANT: Never mention wording 'option', 'choice', or those letters (like 'Option A'). "
            "Focus purely on technical reasons. Under 50 words. Be concise and intuitive. Format:\n"
            "Q1: Concise explanation\nQ2: ..."
        ) if is_reprocess else (
            "Don't mention option letters. Focus on AWS best practices. Under 50 words. Be concise and intuitive.Format:\n"
            "Q1: Explanation\nQ2: ..."
        )

        print(
            f"\n📨 Sending batch of {len(batch)} questions ({len(ENCODER.encode(batch_prompt))} tokens)")

        explanations = send_to_gpt(
            text=batch_prompt,
            prompt=explanation_prompt
        )

        if not explanations:
            print("❌ No GPT response received for batch")
            return

        print("\n📝 GPT Response:")
        print(explanations.strip())
        print("⎯"*40)

        # Parse explanations with question numbering
        explanation_map = {}
        for line in explanations.split('\n'):
            if line.strip().lower().startswith("q"):
                try:
                    parts = line.split(":", 1)
                    q_num = int(parts[0][1:].strip())
                    explanation = parts[1].strip()
                    explanation_map[q_num] = explanation
                except (IndexError, ValueError) as e:
                    print(f"⚠️ Couldn't parse line: {line}")

        # Create bulk operations and log entries
        bulk_ops = []
        for idx, question in enumerate(batch, 1):
            explanation = explanation_map.get(idx)
            if explanation:
                # Add database operation
                bulk_ops.append(UpdateOne(
                    {"_id": question["_id"]},
                    {"$set": {
                        "ai_explanation": explanation,
                        "timestamp_updated": datetime.now(tz)
                    }}
                ))

                # Create log entry
                log_entries.append({
                    "url": question.get('url', 'NO_URL'),
                    "explanation": explanation,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })

        # Write to database
        if bulk_ops:
            result = collection.bulk_write(bulk_ops)
            print(
                f"💾 Saved {len(bulk_ops)} explanations (Matched: {result.matched_count}, Modified: {result.modified_count})")

            # Write to log file
            with open(log_filename, 'w', encoding='utf-8') as log_file:
                log_file.write(
                    f"Explanation Log for {exam_code} ({timestamp})\n")
                log_file.write(f"Processed {len(log_entries)} questions\n\n")

                for entry in log_entries:
                    log_file.write(f"URL: {entry['url']}\n")
                    log_file.write(f"Timestamp: {entry['timestamp']}\n")
                    log_file.write(f"Explanation: {entry['explanation']}\n")
                    log_file.write("-" * 80 + "\n")

            print(f"📝 Saved detailed log to {log_filename}")

    except Exception as e:
        print(f"🔥 Batch processing failed: {str(e)}")
        # Save partial log on error
        if log_entries:
            with open(log_filename, 'w', encoding='utf-8') as log_file:
                log_file.write(f"PARTIAL LOG DUE TO ERROR: {str(e)}\n")
                for entry in log_entries:
                    log_file.write(f"URL: {entry['url']}\n")
                    log_file.write(f"Explanation: {entry['explanation']}\n\n")
            print(f"⚠️ Saved partial log to {log_filename}")


def reprocess_invalid_explanations(exam_code: str, platform: str = "aws"):
    try:
        """Re-process questions containing option letters in explanations"""
        if platform not in PLATFORM_CONFIGS:
            print(f"🔥 Unknown platform: {platform}")
            return

        collection_name = PLATFORM_CONFIGS[platform]['collection']
        collection = qna_db[collection_name]

        query = {
            "code": exam_code,
            "exam.ai_explanation": {"$exists": False},
            "$or": [
                {"ai_explanation": {"$exists": False}},
                {"ai_explanation": ""}
            ],
            "type": {"$in": ["mc", "mc2"]}
        }

        print(f"\n{'='*40}\nRe-checking: {exam_code}\n{'='*40}")

        base_prompt = ""

        def reprocess_batch(batch):
            process_batch(
                batch,
                exam_code,
                collection,
                is_reprocess=True
            )

        process_in_batches(
            questions=list(collection.find(query)),
            base_prompt=base_prompt,
            process_fn=reprocess_batch
        )

    except Exception as e:
        print(f"🔥 Reprocessing error: {str(e)}")
    """ try:
        invalid_questions = list(collection.find(query))
        count = len(invalid_questions)
        print(f"Found {count} questions with invalid explanations")

        if count == 0:
            return

        # Process with enhanced prompt
        batch_size = 20
        for i in range(0, count, batch_size):
            process_batch(
                invalid_questions[i:i+batch_size],
                exam_code,
                collection,
                is_reprocess=True
            )

    except Exception as e:
        print(f"🔥 Reprocessing error: {str(e)}") """


def calculate_batch_tokens(
    questions: List[Dict],
    base_prompt: str
) -> Tuple[int, List[int]]:
    """Calculate token usage for a batch of questions and base prompt"""
    base_tokens = len(ENCODER.encode(base_prompt))
    question_tokens = []

    for question in questions:
        question_text = format_batch_prompt([question])
        question_tokens.append(len(ENCODER.encode(question_text)))

    return base_tokens, question_tokens


def process_in_batches(
    questions: List[Dict],
    base_prompt: str,
    process_fn: Callable,
    token_limit: int = TOKEN_LIMIT
) -> None:
    """Generic batch processor with token limit management"""
    base_tokens, question_tokens = calculate_batch_tokens(
        questions, base_prompt)

    current_batch = []
    current_token_count = 0

    for question, tokens in zip(questions, question_tokens):
        if (current_token_count + tokens + base_tokens) > token_limit:
            if current_batch:
                process_fn(current_batch)
                current_batch = []
                current_token_count = 0

        current_batch.append(question)
        current_token_count += tokens

    if current_batch:
        process_fn(current_batch)


def main():
    """Main function with CLI argument parsing"""
    parser = argparse.ArgumentParser(
        description="AI explanation generator for certification exam questions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all AWS exams
  python explanation.py --platform aws

  # Process all Azure exams
  python explanation.py --platform azure

  # Process specific exam codes
  python explanation.py --exam_codes aws-certified-cloud-practitioner-clf-c02 az-104

  # Process all platforms (default)
  python explanation.py
        """
    )

    parser.add_argument("--platform", choices=['aws', 'azure'], default=None,
                        help="Certification platform to process")
    parser.add_argument("--exam_codes", nargs="+", default=None,
                        help="List of specific exam codes to process")

    args = parser.parse_args()

    print("🚀 Starting AI explanation generation with batched processing")

    # Determine which exam codes to process
    if args.exam_codes:
        # Process specific exam codes
        exam_codes_to_process = args.exam_codes
        print(f"📋 Processing specific exam codes: {len(exam_codes_to_process)}")
        print(f"🔍 Exam codes: {', '.join(exam_codes_to_process)}")

        # Process each exam code (auto-detect platform)
        for code in exam_codes_to_process:
            # Determine platform for this exam code
            platform = detect_platform_for_exam_code(code)
            if platform:
                print(f"\n🎯 Processing {code} (platform: {platform})")
                process_exam_questions(code, platform)
            else:
                print(f"⚠️ Could not determine platform for exam code: {code}")

    elif args.platform:
        # Process all exams for specific platform
        exam_codes_to_process = get_exam_codes_for_platform(args.platform)
        print(f"📋 Processing all {args.platform.upper()} exams: {len(exam_codes_to_process)}")
        print(f"🔍 Exam codes: {', '.join(exam_codes_to_process[:10])}{'...' if len(exam_codes_to_process) > 10 else ''}")

        for code in exam_codes_to_process:
            print(f"\n🎯 Processing {code}")
            process_exam_questions(code, args.platform)

    else:
        # Process all platforms
        all_platforms = get_all_exam_codes()
        total_exams = sum(len(codes) for codes in all_platforms.values())
        print(f"📋 Processing all platforms: {total_exams} total exams")

        for platform_name, exam_codes in all_platforms.items():
            print(f"\n🔥 Processing {platform_name.upper()} platform ({len(exam_codes)} exams)")
            for code in exam_codes:
                print(f"🎯 Processing {code}")
                process_exam_questions(code, platform_name)

    print("\n🎉 All exams processed successfully!")

    # Reprocess invalid explanations
    print("\n🔍 Performing final check for option references...")
    if args.exam_codes:
        for code in exam_codes_to_process:
            platform = detect_platform_for_exam_code(code)
            if platform:
                reprocess_invalid_explanations(code, platform)
    elif args.platform:
        exam_codes_to_process = get_exam_codes_for_platform(args.platform)
        for code in exam_codes_to_process:
            reprocess_invalid_explanations(code, args.platform)
    else:
        all_platforms = get_all_exam_codes()
        for platform_name, exam_codes in all_platforms.items():
            for code in exam_codes:
                reprocess_invalid_explanations(code, platform_name)

    print("\n✅ Final validation complete!")


def detect_platform_for_exam_code(exam_code: str) -> Optional[str]:
    """Detect which platform an exam code belongs to"""
    for platform_name, platform_data in PLATFORM_CONFIGS.items():
        if exam_code in platform_data['config']['exam_codes']:
            return platform_name
    return None


if __name__ == "__main__":
    main()
