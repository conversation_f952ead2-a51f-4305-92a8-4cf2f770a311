import asyncio
from decimal import Decimal
import json
import re
import sys
import traceback
from bson import ObjectId
from pymongo import MongoClient, ReturnDocument, UpdateOne, InsertOne
from pymongo.errors import ConnectionFailure, BulkWriteError
from datetime import datetime, timedelta
import pytz
import gc
import psutil
import platform
import time
import logging
import zstandard as zstd
from bot_soup2 import SoupBot2
import os
import pickle

# Import new multi-platform components
from platforms import get_platform_updater, PLATFORMS
from platforms.aws.updater import AWSUpdater
from platforms.azure.updater import AzureUpdater

# Initialize MongoDB client and databases
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0&compressors=zstd",
    compressors="zstd",
)

log_db = client["log"]
exam_db = client["exam"]
qna_db = client["qna"]
exam_detail_collection = exam_db["detail"]
vendor_collection = exam_db["vendor"]

tz = pytz.timezone("Asia/Hong_Kong")
bot_soup = SoupBot2(
    image_download_path="C:\\Users\\<USER>\\dev\\rn_node\\src\\public\\images",
    qna_images_folder=""
)

# Default exam codes
DEFAULT_EXAM_CODES = [
    "aws-certified-advanced-networking-specialty-ans-c01",
    "aws-certified-ai-practitioner-aif-c01",
    "aws-certified-cloud-practitioner-clf-c02",
    "aws-certified-data-engineer-associate-dea-c01",
    "aws-certified-developer-associate-dva-c02",
    "aws-certified-devops-engineer-professional-dop-c02",
    "aws-certified-machine-learning-engineer-associate-mla-c01",
    "aws-certified-machine-learning-specialty",
    "aws-certified-security-specialty-scs-c02",
    "aws-certified-solutions-architect-associate-saa-c03",
    "aws-certified-solutions-architect-professional-sap-c02",
    "aws-certified-sysops-administrator-associate"
]

DEFAULT_EXAM_CODES = [
    "aws-certified-cloud-practitioner-clf-c02",
]


def custom_converter(o):
    if isinstance(o, datetime):
        return o.isoformat()
    elif isinstance(o, set):
        return list(o)
    elif isinstance(o, bytes):
        return o.decode("utf-8")
    elif isinstance(o, Decimal):
        return float(o)
    elif isinstance(o, complex):
        return {"real": o.real, "imag": o.imag}
    raise TypeError(f"Object of type {type(o)} is not JSON serializable")


def compress_data(data):
    try:
        json_data = json.dumps(data, default=custom_converter)
    except TypeError as e:
        raise TypeError(f"Data is not JSON serializable: {e}")

    compressor = zstd.ZstdCompressor()
    return compressor.compress(json_data.encode("utf-8"))


def custom_decoder(dct):
    for key, value in dct.items():
        if isinstance(value, str):
            try:
                dct[key] = datetime.fromisoformat(value)
            except ValueError:
                pass
        elif isinstance(value, list) and len(value) == 2:
            if all(isinstance(v, (int, float)) for v in value):
                dct[key] = complex(value[0], value[1])
    return dct


def decompress_data(compressed_data):
    if not compressed_data:
        return {}
    if not isinstance(compressed_data, bytes):
        return compressed_data

    decompressor = zstd.ZstdDecompressor()
    json_data = decompressor.decompress(compressed_data).decode("utf-8")
    try:
        return json.loads(json_data, object_hook=custom_decoder)
    except json.JSONDecodeError as e:
        raise ValueError(f"Failed to decode JSON: {e}")


def log_action(collection_name, action, **kwargs):
    log_db[collection_name].insert_one(
        {"action": action, "timestamp": datetime.now(tz), **kwargs}
    )


async def get_exam_codes_and_provider(exam_codes=None):
    """Get exam codes and their provider information (Part A logic)"""
    if exam_codes is None:
        exam_codes = DEFAULT_EXAM_CODES
    print(f"Processing exam codes: {exam_codes}")

    # Get provider names and slugs
    providers = bot_soup.get_provider_names_and_slugs()
    # print(f"Found providers: {providers}")
    if not providers:
        print("Warning: No providers found!")
        return {}

    provider_slugs = [provider_slug for _, provider_slug in providers]
    # print(f"Provider slugs: {provider_slugs}")

    # Get exam info for all providers
    print("Fetching exam info for providers...")
    all_exam_infos = await bot_soup.process_multiple_pages(
        provider_slugs,
        bot_soup.get_exam_info_new,
    )
    # print(f"Received exam infos: {all_exam_infos}")

    # Create mapping of exam codes to their providers
    exam_provider_map = {}
    for provider_slug, exams in zip(provider_slugs, all_exam_infos):
        if not exams:
            print(f"No exams found for provider: {provider_slug}")
            continue

        for exam in exams:
            exam_code = exam.get("exam_code")
            if not exam_code:
                print("Warning: Exam missing exam_code field")
                continue

            # print(f"Checking exam: {exam_code}")
            if exam_code in exam_codes:
                print(
                    f"Matched exam code: {exam_code} with provider: {provider_slug}")
                exam_provider_map[exam_code] = provider_slug
            """ else:
                print(f"Exam code {exam_code} not in target list") """

    print(f"Final exam_provider_map: {exam_provider_map}")
    return exam_provider_map

# Add this function to handle caching of provider discussion links


def get_cached_discussion_links(provider_slug, max_cache_age_hours=24):
    """Get cached discussion links for a provider or return None if cache is invalid"""
    cache_dir = "cache"
    os.makedirs(cache_dir, exist_ok=True)
    cache_file = os.path.join(cache_dir, f"{provider_slug}_discussions.pkl")

    # Check if cache file exists and is not too old
    if os.path.exists(cache_file):
        file_age = datetime.now(
            tz) - datetime.fromtimestamp(os.path.getmtime(cache_file), tz)
        if file_age.total_seconds() < max_cache_age_hours * 3600:
            try:
                with open(cache_file, "rb") as f:
                    cached_links = pickle.load(f)
                    print(
                        f"Loaded {len(cached_links)} cached discussion links for {provider_slug}")
                    return cached_links
            except Exception as e:
                print(f"Error loading cached discussions: {e}")

    return None


def save_discussion_links_cache(provider_slug, links):
    """Save discussion links to cache file"""
    cache_dir = "cache"
    os.makedirs(cache_dir, exist_ok=True)
    cache_file = os.path.join(cache_dir, f"{provider_slug}_discussions.pkl")

    try:
        with open(cache_file, "wb") as f:
            pickle.dump(links, f)
        print(f"Cached {len(links)} discussion links for {provider_slug}")
    except Exception as e:
        print(f"Error caching discussions: {e}")


async def update_exam_details(exam_codes=None):
    """Update exam details for specified codes (Part B logic)"""
    exam_provider_map = await get_exam_codes_and_provider(exam_codes)

    # Get exam details for each exam code
    name_info_pairs = [(provider_slug, code)
                       for code, provider_slug in exam_provider_map.items()]

    scrapped_exam_details = await bot_soup.process_multiple_pages(
        name_info_pairs,
        bot_soup.get_exam_details_wrapper,
    )

    # Update exam details in database
    exam_detail_operations = []
    for exam_detail in scrapped_exam_details:
        if exam_detail is None:
            continue

        exam_detail_operations.append(
            UpdateOne(
                {"code": exam_detail["code"]},
                {
                    "$set": {
                        "vendor_code": exam_detail["vendor_code"],
                        "name": exam_detail["name"],
                        "claimed_total": exam_detail["claimed_total"],
                        "date_updated": exam_detail["date_updated"],
                        "timestamp_updated": datetime.now(tz),
                    }
                },
                upsert=True
            )
        )

    if exam_detail_operations:
        exam_detail_collection.bulk_write(exam_detail_operations)

    return exam_provider_map


async def update_qna(exam_codes=None):
    """Update QNA for specified exam codes (Part C logic)"""
    print("Starting update_qna...")
    exam_provider_map = await update_exam_details(exam_codes)
    print(f"Got exam_provider_map: {exam_provider_map}")

    if not exam_provider_map:
        print("No exam_provider_map returned, exiting")
        return

    qna_operations = []
    # Cache discussion links by provider to avoid repeated scans
    provider_discussion_links = {}

    for code, provider_slug in exam_provider_map.items():
        # Get discussion links for this provider if not already cached
        if provider_slug not in provider_discussion_links:
            print(f"Getting discussion links for provider: {provider_slug}")

            # Try to get from cache first
            cached_links = get_cached_discussion_links(provider_slug)
            if cached_links:
                provider_discussion_links[provider_slug] = cached_links
            else:
                # If not in cache, scrape them
                link = f"discussions/{provider_slug}/"
                last_page = bot_soup.get_total_pages(link)
                print(f"Total pages for {link}: {last_page}")
                if last_page is None:
                    print(f"Failed to get total pages for {link}")
                    log_action("exception", "404", link=link)
                    continue

                discussion_links = await bot_soup.process_multiple_pages(
                    [f"{link}{x}" for x in range(1, last_page + 1)],
                    bot_soup.get_discussion_links_per_page,
                )
                provider_discussion_links[provider_slug] = [
                    item for sublist in discussion_links
                    if sublist is not None for item in sublist
                ]

                # Save to cache
                save_discussion_links_cache(
                    provider_slug, provider_discussion_links[provider_slug])

        # Use cached discussion links for this provider
        discussion_links = provider_discussion_links[provider_slug]

        # Filter links for this exam code
        normalized_code = code.replace("_", "-")
        print(
            f"Filtering {len(discussion_links)} links for exam code: {code} (normalized: {normalized_code})")

        # Add more robust error handling and debugging
        exam_discussion_links = []
        try:
            pattern = r"(?<!\w)" + re.escape(normalized_code) + r"(?!\w)"
            print(f"Using regex pattern: {pattern}")

            # Process links in smaller batches to avoid memory issues
            batch_size = 1000
            for i in range(0, len(discussion_links), batch_size):
                batch = discussion_links[i:i+batch_size]
                print(
                    f"Processing batch {i//batch_size + 1} of {(len(discussion_links) + batch_size - 1) // batch_size}")

                for link in batch:
                    try:
                        normalized_link = link.replace("_", "-")
                        if (re.search(pattern, normalized_link, re.IGNORECASE) or
                            (code == "aws-certified-machine-learning-engineer-associate-mla-c01" and
                             "aws-certified-machine-learning-engineer-associate-mla" in link)):
                            exam_discussion_links.append(link)
                    except Exception as e:
                        print(f"Error processing link: {link}")
                        print(f"Error details: {str(e)}")
                        # Continue with next link instead of crashing
                        continue

                print(
                    f"Found {len(exam_discussion_links)} matching links so far")
        except Exception as e:
            print(f"Error during link filtering: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            # Return empty list if there's an error in the filtering process
            exam_discussion_links = []

        print(
            f"Processing {len(exam_discussion_links)} discussion links for exam: {code}")
        # Process QNA for this exam
        pages_processed = await bot_soup.process_multiple_pages(exam_discussion_links)
        print(
            f"Processed {len([p for p in pages_processed if p is not None])} pages successfully")

        # Prepare log file for this exam code
        log_filename = f"qna_updates_{provider_slug}_{code}_{datetime.now(tz).strftime('%Y%m%d_%H%M%S')}.json"
        update_log = {
            "exam_code": code,
            "timestamp": datetime.now(tz).isoformat(),
            "updated_records": []
        }

        for i, url in enumerate(exam_discussion_links):
            if pages_processed[i] is not None:
                # Create update record
                update_record = {
                    "url": url,
                    "update_time": datetime.now(tz).isoformat(),
                    "update_data": pages_processed[i]
                }
                update_log["updated_records"].append(update_record)

                # First, check if the document exists and get its current ai_explanation value
                existing_doc = qna_db[provider_slug].find_one({"url": url})

                # Prepare the update operation with conditional logic for ai_explanation
                if existing_doc:
                    # If document exists, check if exam.ai_explanation exists and is not None
                    if existing_doc.get('exam', {}).get('ai_explanation') is not None:
                        # Use the existing exam.ai_explanation value
                        ai_explanation_value = existing_doc['exam']['ai_explanation']
                    else:
                        # Otherwise use the current ai_explanation value if it exists
                        ai_explanation_value = existing_doc.get('ai_explanation')
                else:
                    # For new documents, there's no existing value to preserve
                    ai_explanation_value = None

                # Add the update operation with the determined ai_explanation value
                update_doc = {
                    "code": code,
                    "timestamp_updated": datetime.now(tz)
                }

                # Only include ai_explanation in the update if it has a value
                if ai_explanation_value is not None:
                    update_doc["ai_explanation"] = ai_explanation_value

                qna_operations.append(
                    UpdateOne(
                        {"url": url},
                        {"$set": update_doc},
                        upsert=True
                    )
                )

                # Add to bulk operations
                qna_operations.append(
                    UpdateOne(
                        {"url": url},
                        {
                            "$set": {
                                "code": code,
                                "timestamp_updated": datetime.now(tz),
                                **pages_processed[i]
                            }
                        },
                        upsert=True
                    )
                )

        # Save log file
        with open(log_filename, "w") as log_file:
            json.dump(update_log, log_file, indent=2)
        print(f"Saved detailed update log to {log_filename}")

        # Update exam detail with total questions count
        update_result = exam_detail_collection.update_one(
            {"code": code},
            {
                "$set": {
                    "total_questions": qna_db[provider_slug].count_documents({"code": code}),
                    # we just handle mc and mc2 on frontend at the moment. Use this digit for user.
                    "total_questions_mc_or_mc2":
                    qna_db[provider_slug].count_documents({
                        "code": code,
                        "subject": {"$exists": True},
                        "type": {"$in": ['mc', 'mc2']}
                    }),
                    "date_last_complete_scan": datetime.now(tz).strftime("%Y-%m-%d"),
                    "timestamp_updated": datetime.now(tz)
                }
            }
        )
        print(
            f"Exam detail update result: matched={update_result.matched_count}, modified={update_result.modified_count}")

    if qna_operations:
        # Group operations by provider to bulk write to correct collections
        operations_by_provider = {}
        for op in qna_operations:
            # Extract code from the filter, handling potential missing keys
            filter_dict = op._filter
            url = filter_dict.get("url", "")
            # Extract code from the update document
            update_doc = op._doc.get("$set", {})
            code = update_doc.get("code")

            # If we have the code, we can directly map to provider
            if code and code in exam_provider_map:
                provider = exam_provider_map[code]
                if provider not in operations_by_provider:
                    operations_by_provider[provider] = []
                operations_by_provider[provider].append(op)
            else:
                # Fallback: try to find provider by matching URL patterns
                for exam_code, provider in exam_provider_map.items():
                    # Check if URL contains the exam code
                    normalized_code = exam_code.replace("_", "-")
                    if (re.search(r"(?<!\w)" + re.escape(normalized_code) + r"(?!\w)",
                                  url.replace("_", "-"), re.IGNORECASE) or
                        (exam_code == "aws-certified-machine-learning-engineer-associate-mla-c01" and
                         "aws-certified-machine-learning-engineer-associate-mla" in url)):
                        if provider not in operations_by_provider:
                            operations_by_provider[provider] = []
                        operations_by_provider[provider].append(op)
                        break

        # Execute bulk writes and verify results
        for provider_slug, ops in operations_by_provider.items():
            try:
                print(
                    f"Executing {len(ops)} operations for provider {provider_slug}")
                # Log a sample operation to debug
                """ if ops:
                    sample_op = ops[0]
                    print(f"Sample operation - Filter: {sample_op._filter}, Update: {sample_op._doc}") """

                result = qna_db[provider_slug].bulk_write(ops)
                print(
                    f"Successfully updated {result.modified_count} documents in {provider_slug} collection")
                print(f"Inserted {result.inserted_count} new documents")

                # Verify updates
                updated_urls = [op._filter['url']
                                for op in ops if isinstance(op, UpdateOne)]
                if updated_urls:
                    updated_count = qna_db[provider_slug].count_documents({
                        'url': {'$in': updated_urls},
                        'timestamp_updated': {'$gte': datetime.now(tz) - timedelta(minutes=1)}
                    })
                    print(
                        f"Verified {updated_count}/{len(updated_urls)} updates in database")

                log_action(
                    "qna_update",
                    "bulk_write",
                    provider_slug=provider_slug,
                    modified_count=result.modified_count,
                    inserted_count=result.inserted_count,
                    operation_count=len(ops)
                )
            except BulkWriteError as bwe:
                print(f"Bulk write error for {provider_slug}:")
                print(bwe.details)
                log_action(
                    "exception",
                    "bulk_write_error",
                    provider_slug=provider_slug,
                    error=str(bwe),
                    details=bwe.details
                )
            except Exception as e:
                print(f"Error updating {provider_slug} collection: {str(e)}")
                log_action(
                    "exception",
                    "update_error",
                    provider_slug=provider_slug,
                    error=str(e)
                )


async def main(exam_codes=None):
    """Main function to update QNA and exam details for specified codes"""
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    try:
        await update_qna(exam_codes)
    except Exception as e:
        print(f"An error occurred: {e}")
        log_action("exception", type(e).__name__, message=str(e))
    finally:
        await bot_soup.session_close()

async def main_multi_platform(platform_name: str = None, exam_codes: list = None,
                              category: str = None, list_platforms: bool = False,
                              platform_info: str = None):
    """
    Main function for multi-platform QNA updates.

    Args:
        platform_name: Platform to update ('aws', 'azure')
        exam_codes: Optional list of specific exam codes
        category: Optional category filter
        list_platforms: Whether to list available platforms
        platform_info: Platform to show info for
    """
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    try:
        # List available platforms
        if list_platforms:
            print("Available platforms:")
            for name, updater_class in PLATFORMS.items():
                print(f"  - {name}: {updater_class.__doc__ or 'No description'}")
            return

        # Show platform info
        if platform_info:
            if platform_info not in PLATFORMS:
                print(f"Unknown platform: {platform_info}. Available: {list(PLATFORMS.keys())}")
                return

            updater_class = get_platform_updater(platform_info)
            async with updater_class() as updater:
                info = updater.get_platform_info()
                print(f"\nPlatform: {info['platform_name']}")
                print(f"Description: {info.get('description', 'N/A')}")
                print(f"Provider Slug: {info['provider_slug']}")
                print(f"Total Exams: {info['exam_count']}")

                if 'categories' in info:
                    print("\nCategories:")
                    for cat, exams in info['categories'].items():
                        print(f"  {cat}: {len(exams)} exams")

                if 'total_exams_by_category' in info:
                    print("\nExam counts by category:")
                    for cat, count in info['total_exams_by_category'].items():
                        print(f"  {cat}: {count}")
            return

        # Default to AWS for backward compatibility
        if not platform_name:
            platform_name = 'aws'
            print(f"No platform specified, defaulting to {platform_name}")

        # Validate platform
        if platform_name not in PLATFORMS:
            print(f"Unknown platform: {platform_name}. Available: {list(PLATFORMS.keys())}")
            return

        # Get the appropriate updater
        updater_class = get_platform_updater(platform_name)

        # Run the update
        async with updater_class() as updater:
            print(f"Starting QNA update for {platform_name} platform...")

            # Use platform-specific update methods if available
            if hasattr(updater, f'update_{platform_name}_exams'):
                update_method = getattr(updater, f'update_{platform_name}_exams')
                success = await update_method(exam_codes=exam_codes, category=category)
            else:
                success = await updater.update_qna(exam_codes)

            if success:
                print(f"QNA update completed successfully for {platform_name}")
            else:
                print(f"QNA update failed for {platform_name}")

    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Multi-platform QNA updater for certification exams",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Update AWS exams (backward compatible)
  python qna_updater.py
  python qna_updater.py --exam_codes aws-certified-cloud-practitioner-clf-c02

  # Update specific platform
  python qna_updater.py --platform aws
  python qna_updater.py --platform azure

  # Update by category
  python qna_updater.py --platform aws --category foundational
  python qna_updater.py --platform azure --category fundamentals

  # List available platforms
  python qna_updater.py --list-platforms

  # Show platform information
  python qna_updater.py --platform-info aws
  python qna_updater.py --platform-info azure
        """
    )

    # Backward compatibility arguments
    parser.add_argument("--exam_codes", nargs="+", default=None,
                        help="List of exam codes to process")

    # New multi-platform arguments
    parser.add_argument("--platform", choices=['aws', 'azure'], default=None,
                        help="Certification platform to update")
    parser.add_argument("--category", type=str, default=None,
                        help="Exam category to filter by (e.g., foundational, associate, expert)")
    parser.add_argument("--list-platforms", action="store_true",
                        help="List available platforms")
    parser.add_argument("--platform-info", type=str, default=None,
                        help="Show information about a specific platform")

    args = parser.parse_args()

    # Check if using new multi-platform features
    if (args.platform or args.category or args.list_platforms or args.platform_info):
        # Use new multi-platform main function
        asyncio.run(main_multi_platform(
            platform_name=args.platform,
            exam_codes=args.exam_codes,
            category=args.category,
            list_platforms=args.list_platforms,
            platform_info=args.platform_info
        ))
    else:
        # Use legacy main function for backward compatibility
        asyncio.run(main(args.exam_codes))
