"""
Platform-specific QNA updater implementations.

This module contains platform-specific implementations for different
certification providers (AWS, Azure, etc.).
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from platforms.base_platform import BasePlatform
from platforms.aws.updater import AWSUpdater
from platforms.azure.updater import AzureUpdater

# Platform registry for easy access
PLATFORMS = {
    'aws': AWSUpdater,
    'azure': AzureUpdater,
}

def get_platform_updater(platform_name):
    """Get the updater class for a specific platform."""
    if platform_name not in PLATFORMS:
        raise ValueError(f"Unknown platform: {platform_name}. Available platforms: {list(PLATFORMS.keys())}")
    return PLATFORMS[platform_name]

__all__ = ['BasePlatform', 'AWSUpdater', 'AzureUpdater', 'PLATFORMS', 'get_platform_updater']
