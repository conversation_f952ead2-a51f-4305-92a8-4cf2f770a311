from datetime import datetime
from pymongo import MongoClient, UpdateOne
import pytz
import requests
import random
import time
import json
import sys
import tiktoken
import os
from typing import Callable, Optional, List, Dict, Tuple

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
qna_db = client["qna"]
exam_db = client["exam"]
tz = pytz.timezone("Asia/Hong_Kong")

# Configuration
PREDEFINED_EXAM_CODES = []
""" PREDEFINED_EXAM_CODES = [
    "aws-certified-advanced-networking-specialty-ans-c01",
    "aws-certified-ai-practitioner-aif-c01",
    "aws-certified-cloud-practitioner-clf-c02",
    "aws-certified-data-engineer-associate-dea-c01",
    "aws-certified-developer-associate-dva-c02",
    "aws-certified-devops-engineer-professional-dop-c02",
    "aws-certified-machine-learning-engineer-associate-mla-c01",
    "aws-certified-machine-learning-specialty",
    "aws-certified-security-specialty-scs-c02",
    "aws-certified-solutions-architect-associate-saa-c03",
    "aws-certified-solutions-architect-professional-sap-c02",
    "aws-certified-sysops-administrator-associate"
] """
TOKEN_LIMIT = 3800
ENCODER = tiktoken.encoding_for_model("gpt-4o-mini")


def get_popular_exam_codes() -> List[str]:
    """Retrieve popular Amazon exam codes from database"""
    detail_collection = exam_db["detail"]
    try:
        # Find exams matching popularity criteria
        popular_exams = detail_collection.find({
            "vendor_code": "amazon",
            "is_popular": True
        })

        # Extract and validate codes
        return [exam["code"] for exam in popular_exams if exam.get("code")]

    except Exception as e:
        print(f"🔥 Error fetching popular exams: {str(e)}")
        return []


def send_to_gpt(
    text: str,
    prompt: str,
    bearer_tokens: List[str] = [
        "sk-yOGPPghWJXYZs4mQ9hP4XKPz5EoSHZPCxH9kQk3W05G2JK1F",
        "sk-lmab8Wl6AVU3VLKjsoGTP4H3zEK1CSV9HE1H7W7eOq3HXFPi"
    ],
    model: str = "gpt-4.1-mini",
    max_retries: int = 30
) -> Optional[str]:
    """Enhanced GPT API request with token management"""
    error_data = {
        "system_prompt": text,
        "user_prompt": prompt,
        "model": model,
        "errors": []
    }

    json_payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": text},
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7
    }

    if not bearer_tokens:
        print("🚨 Critical: No valid API tokens provided", file=sys.stderr)
        sys.exit(1)

    active_tokens = set(bearer_tokens)
    disabled_tokens = set()
    last_working_token = None

    for attempt in range(max_retries + 1):
        if not active_tokens:
            print("🔥 All API tokens exhausted", file=sys.stderr)
            sys.exit(1)

        current_token = (
            last_working_token
            if last_working_token and last_working_token in active_tokens
            else random.choice(list(active_tokens))
        )
        token_suffix = current_token[-6:]

        try:
            response = requests.post(
                "https://api.chatanywhere.tech/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {current_token}",
                    "Content-Type": "application/json"
                },
                json=json_payload,
                timeout=45
            )
            response.raise_for_status()

            content = response.json().get("choices", [{}])[
                0].get("message", {}).get("content", "")
            return content if content else None

        except requests.exceptions.RequestException as e:
            status_code = getattr(response, 'status_code', None)
            response_text = getattr(response, 'text', 'No response')

            error_entry = {
                "attempt": attempt + 1,
                "token_suffix": token_suffix,
                "status": status_code,
                "error": str(e),
                "response": response_text[:500] + "..." if len(response_text) > 500 else response_text
            }
            error_data["errors"].append(error_entry)

            if status_code in [401, 403]:
                disabled_tokens.add(current_token)
                active_tokens.discard(current_token)
            elif status_code == 429:
                if "free account is limited" in response_text:
                    disabled_tokens.add(current_token)
                    active_tokens.discard(current_token)

            if attempt >= max_retries:
                print("🔥 Maximum retries exhausted")
                sys.exit(1)

            sleep_time = random.uniform(0.5, 1.5) * (2 ** min(attempt//2, 5))
            time.sleep(sleep_time)

    return None


def format_batch_prompt(questions: List[Dict]) -> str:
    """Format a batch of questions for GPT processing with enhanced validation"""
    batch_prompt = ""
    for q_idx, question in enumerate(questions, 1):
        try:
            q_data = question['exam']
            prompt = f"## Question {q_idx}:\n{q_data['question']}\n"

            # Validate choices format
            if not isinstance(q_data['choices'], list):
                raise ValueError("Choices is not a list")

            for choice in q_data['choices']:
                if not isinstance(choice, dict):
                    raise ValueError("Choice is not a dictionary")
                for letter, text in choice.items():
                    prompt += f"{letter}: {text}\n"

            # Validate answers
            if not isinstance(q_data['answer'], list):
                raise ValueError("Answers is not a list")

            prompt += f"\nCorrect Answer(s): {', '.join(q_data['answer'])}\n\n"
            batch_prompt += prompt

        except (KeyError, ValueError) as e:
            print(f"🛑 Skipping question {question.get('url')} - {str(e)}")
            continue

    return batch_prompt


def process_exam_questions(exam_code: str):
    """Process all questions using improved token-aware batching"""
    collection = qna_db["amazon"]
    query = {
        "code": exam_code,
        "exam.ai_explanation": {"$exists": False},
        "$or": [
            {"ai_explanation": {"$exists": False}},
            {"ai_explanation": ""}
        ],
        "exam.choices": {"$exists": True, "$ne": []},
        "exam.answer": {"$exists": True, "$ne": []},
        "type": {"$in": ["mc", "mc2"]}
    }

    print(f"\n{'='*40}\nProcessing: {exam_code}\n{'='*40}")

    try:
        # Get total count for progress tracking
        total_questions = collection.count_documents(query)
        print(f"Found {total_questions} questions needing explanations")

        # Retrieve and validate all questions first
        valid_questions = []
        processed_count = 0
        cursor = collection.find(query).batch_size(100)

        for question in cursor:
            try:
                processed_count += 1
                if processed_count % 50 == 0:
                    print(
                        f"⏳ Validated {processed_count}/{total_questions} questions...")

                # Validate question structure
                qexam = question.get('exam', {})
                if not all(key in qexam for key in ['choices', 'answer']):
                    print(
                        f"⚠️ Skipping malformed question: {question.get('url', 'No URL')}")
                    continue

                valid_questions.append(question)

            except Exception as e:
                print(
                    f"🛑 Error validating question {question.get('url')}: {str(e)}")
                continue

        print(
            f"✅ Validated {len(valid_questions)}/{total_questions} questions")

        # Configure batch processing
        base_prompt = ""

        def batch_processor(batch: List[Dict]):
            """Closure to handle batch processing with context"""
            process_batch(batch, exam_code, collection)

        # Process using reusable token-aware batcher
        process_in_batches(
            questions=valid_questions,
            base_prompt=base_prompt,
            process_fn=batch_processor,
            token_limit=TOKEN_LIMIT
        )

        print(
            f"✅ Completed {exam_code}: Processed {len(valid_questions)} explanations")
        if total_questions > len(valid_questions):
            print(
                f"⚠️  {total_questions - len(valid_questions)} questions skipped due to errors")

    except Exception as e:
        print(f"🔥 Critical error processing {exam_code}: {str(e)}")
        raise


def process_batch(batch: List[Dict], exam_code: str, collection, is_reprocess: bool = False):
    """Process a single batch of questions with logging"""
    # Create explanations directory if not exists
    os.makedirs('explanations', exist_ok=True)
    log_entries = []
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = os.path.join(
        # Modified path
        "explanations", f"explanations_log_{exam_code}_{timestamp}.txt")

    try:
        batch_prompt = format_batch_prompt(batch)
        explanation_prompt = (
            "IMPORTANT: Never mention wording 'option', 'choice', or those letters (like 'Option A'). "
            "Focus purely on technical reasons. Under 50 words. Be concise and intuitive. Format:\n"
            "Q1: Concise explanation\nQ2: ..."
        ) if is_reprocess else (
            "Don't mention option letters. Focus on AWS best practices. Under 50 words. Be concise and intuitive.Format:\n"
            "Q1: Explanation\nQ2: ..."
        )

        print(
            f"\n📨 Sending batch of {len(batch)} questions ({len(ENCODER.encode(batch_prompt))} tokens)")

        explanations = send_to_gpt(
            text=batch_prompt,
            prompt=explanation_prompt
        )

        if not explanations:
            print("❌ No GPT response received for batch")
            return

        print("\n📝 GPT Response:")
        print(explanations.strip())
        print("⎯"*40)

        # Parse explanations with question numbering
        explanation_map = {}
        for line in explanations.split('\n'):
            if line.strip().lower().startswith("q"):
                try:
                    parts = line.split(":", 1)
                    q_num = int(parts[0][1:].strip())
                    explanation = parts[1].strip()
                    explanation_map[q_num] = explanation
                except (IndexError, ValueError) as e:
                    print(f"⚠️ Couldn't parse line: {line}")

        # Create bulk operations and log entries
        bulk_ops = []
        for idx, question in enumerate(batch, 1):
            explanation = explanation_map.get(idx)
            if explanation:
                # Add database operation
                bulk_ops.append(UpdateOne(
                    {"_id": question["_id"]},
                    {"$set": {
                        "ai_explanation": explanation,
                        "timestamp_updated": datetime.now(tz)
                    }}
                ))

                # Create log entry
                log_entries.append({
                    "url": question.get('url', 'NO_URL'),
                    "explanation": explanation,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })

        # Write to database
        if bulk_ops:
            result = collection.bulk_write(bulk_ops)
            print(
                f"💾 Saved {len(bulk_ops)} explanations (Matched: {result.matched_count}, Modified: {result.modified_count})")

            # Write to log file
            with open(log_filename, 'w', encoding='utf-8') as log_file:
                log_file.write(
                    f"Explanation Log for {exam_code} ({timestamp})\n")
                log_file.write(f"Processed {len(log_entries)} questions\n\n")

                for entry in log_entries:
                    log_file.write(f"URL: {entry['url']}\n")
                    log_file.write(f"Timestamp: {entry['timestamp']}\n")
                    log_file.write(f"Explanation: {entry['explanation']}\n")
                    log_file.write("-" * 80 + "\n")

            print(f"📝 Saved detailed log to {log_filename}")

    except Exception as e:
        print(f"🔥 Batch processing failed: {str(e)}")
        # Save partial log on error
        if log_entries:
            with open(log_filename, 'w', encoding='utf-8') as log_file:
                log_file.write(f"PARTIAL LOG DUE TO ERROR: {str(e)}\n")
                for entry in log_entries:
                    log_file.write(f"URL: {entry['url']}\n")
                    log_file.write(f"Explanation: {entry['explanation']}\n\n")
            print(f"⚠️ Saved partial log to {log_filename}")


def reprocess_invalid_explanations(exam_code: str):
    try:
        """Re-process questions containing option letters in explanations"""
        collection = qna_db["amazon"]

        query = {
            "code": exam_code,
            "exam.ai_explanation": {"$exists": False},
            "$or": [
                {"ai_explanation": {"$exists": False}},
                {"ai_explanation": ""}
            ],
            "type": {"$in": ["mc", "mc2"]}
        }

        print(f"\n{'='*40}\nRe-checking: {exam_code}\n{'='*40}")

        base_prompt = ""

        def reprocess_batch(batch):
            process_batch(
                batch,
                exam_code,
                collection,
                is_reprocess=True
            )

        process_in_batches(
            questions=list(collection.find(query)),
            base_prompt=base_prompt,
            process_fn=reprocess_batch
        )

    except Exception as e:
        print(f"🔥 Reprocessing error: {str(e)}")
    """ try:
        invalid_questions = list(collection.find(query))
        count = len(invalid_questions)
        print(f"Found {count} questions with invalid explanations")

        if count == 0:
            return

        # Process with enhanced prompt
        batch_size = 20
        for i in range(0, count, batch_size):
            process_batch(
                invalid_questions[i:i+batch_size],
                exam_code,
                collection,
                is_reprocess=True
            )

    except Exception as e:
        print(f"🔥 Reprocessing error: {str(e)}") """


def calculate_batch_tokens(
    questions: List[Dict],
    base_prompt: str
) -> Tuple[int, List[int]]:
    """Calculate token usage for a batch of questions and base prompt"""
    base_tokens = len(ENCODER.encode(base_prompt))
    question_tokens = []

    for question in questions:
        question_text = format_batch_prompt([question])
        question_tokens.append(len(ENCODER.encode(question_text)))

    return base_tokens, question_tokens


def process_in_batches(
    questions: List[Dict],
    base_prompt: str,
    process_fn: Callable,
    token_limit: int = TOKEN_LIMIT
) -> None:
    """Generic batch processor with token limit management"""
    base_tokens, question_tokens = calculate_batch_tokens(
        questions, base_prompt)

    current_batch = []
    current_token_count = 0

    for question, tokens in zip(questions, question_tokens):
        if (current_token_count + tokens + base_tokens) > token_limit:
            if current_batch:
                process_fn(current_batch)
                current_batch = []
                current_token_count = 0

        current_batch.append(question)
        current_token_count += tokens

    if current_batch:
        process_fn(current_batch)


if __name__ == "__main__":
    print("🚀 Starting AI explanation generation with batched processing")

    # Combine static and dynamic exam codes
    dynamic_codes = get_popular_exam_codes()
    all_exam_codes = list(dict.fromkeys(PREDEFINED_EXAM_CODES + dynamic_codes))

    print(f"📋 Total exams to process: {len(all_exam_codes)}")
    print(f"🔍 Exam codes: {', '.join(all_exam_codes)}")

    for code in all_exam_codes:
        process_exam_questions(code)

    print("\n🎉 All exams processed successfully!")

    print("\n🔍 Performing final check for option references...")
    for code in all_exam_codes:
        reprocess_invalid_explanations(code)
    print("\n✅ Final validation complete!")
