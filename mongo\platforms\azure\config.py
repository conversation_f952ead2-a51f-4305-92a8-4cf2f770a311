"""
Azure certification platform configuration.
"""

# Azure exam codes - comprehensive list of current Microsoft Azure certifications
AZURE_EXAM_CODES = [
    # Fundamentals
    "az-900",  # Azure Fundamentals
    "ai-900",  # AI Fundamentals
    "dp-900",  # Data Fundamentals
    #"pl-900",  # Power Platform Fundamentals
    #"sc-900",  # Security, Compliance, and Identity Fundamentals
    #"ms-900",  # Microsoft 365 Fundamentals
    
    # Associate Level
    "az-104",  # Azure Administrator Associate
    "az-204",  # Azure Developer Associate
    "az-305",  # Azure Solutions Architect Expert (requires AZ-104 or AZ-204)
    "az-400",  # Azure DevOps Engineer Expert
    "az-500",  # Azure Security Engineer Associate
    "az-700",  # Azure Network Engineer Associate
    #"az-800",  # Windows Server Hybrid Administrator Associate
    #"az-801",  # Windows Server Hybrid Administrator Associate
    
    # Expert Level
    #"az-120",  # SAP on Azure
    "az-140",  # Windows Virtual Desktop
    #"az-220",  # IoT Developer
    #"az-600",  # Stack HCI
    #"az-720",  # Troubleshooting Microsoft Azure Connectivity
    
    # Data & AI
    "ai-102",  # AI Engineer Associate
    "dp-100",  # Data Scientist Associate
    "dp-203",  # Data Engineer Associate
    "dp-300",  # Database Administrator Associate
    #"dp-420",  # Fabric Analytics Engineer Associate
    #"dp-500",  # Enterprise Data Analyst Associate
    #"dp-600",  # Fabric Analytics Engineer Associate
    #"dp-700",  # Fabric Analytics Engineer Associate
]

""" # Power Platform
    "pl-100",  # Power Platform App Maker Associate
    "pl-200",  # Power Platform Functional Consultant Associate
    "pl-300",  # Power BI Data Analyst Associate
    "pl-400",  # Power Platform Developer Associate
    "pl-500",  # Power Automate RPA Developer Associate
    "pl-600",  # Power Platform Solution Architect Expert
    
    # Security
    "sc-100",  # Cybersecurity Architect Expert
    "sc-200",  # Security Operations Analyst Associate
    "sc-300",  # Identity and Access Administrator Associate
    "sc-400",  # Information Protection Administrator Associate
    "sc-401",  # Information Protection and Governance Associate
    
    # Microsoft 365
    "ms-100",  # Microsoft 365 Identity and Services
    "ms-101",  # Microsoft 365 Mobility and Security
    "ms-102",  # Microsoft 365 Administrator Expert
    "ms-203",  # Microsoft 365 Messaging
    "ms-220",  # Troubleshooting Microsoft Exchange Online
    "ms-500",  # Microsoft 365 Security Administration
    "ms-600",  # Building Applications and Solutions with Microsoft 365 Core Services
    "ms-700",  # Managing Microsoft Teams
    "ms-720",  # Microsoft Teams Voice Engineer Expert
    "ms-721",  # Collaboration Communications Systems Engineer Associate
    
    # Modern Desktop
    "md-100",  # Windows Client
    "md-101",  # Managing Modern Desktops
    "md-102",  # Endpoint Administrator Associate
    
    # Dynamics 365
    "mb-210",  # Dynamics 365 Sales Functional Consultant Associate
    "mb-220",  # Dynamics 365 Marketing Functional Consultant Associate
    "mb-230",  # Dynamics 365 Customer Service Functional Consultant Associate
    "mb-240",  # Dynamics 365 Field Service Functional Consultant Associate
    "mb-260",  # Dynamics 365 Customer Insights (Journeys) Functional Consultant Associate
    "mb-300",  # Dynamics 365 Core Finance and Operations
    "mb-310",  # Dynamics 365 Finance Functional Consultant Associate
    "mb-320",  # Dynamics 365 Supply Chain Management, Manufacturing
    "mb-330",  # Dynamics 365 Supply Chain Management Functional Consultant Associate
    "mb-335",  # Dynamics 365 Supply Chain Management Expert
    "mb-500",  # Dynamics 365 Finance and Operations Apps Developer Associate
    "mb-700",  # Dynamics 365 Finance and Operations Apps Solution Architect Expert
    "mb-800",  # Dynamics 365 Business Central Functional Consultant Associate
    "mb-820",  # Dynamics 365 Business Central Developer Associate
    "mb-910",  # Dynamics 365 Fundamentals (CRM)
    "mb-920",  # Dynamics 365 Fundamentals (ERP) """
    
# Azure-specific special matching rules (if any)
AZURE_SPECIAL_MATCHING_RULES = {
    # Add any Azure-specific URL matching rules here
    # Example: "az-305": {"alternative_patterns": ["az305", "azure-solutions-architect"]}
}

# Azure platform configuration
AZURE_CONFIG = {
    "platform_name": "azure",
    "provider_slug": "microsoft",
    "exam_codes": AZURE_EXAM_CODES,
    "special_matching_rules": AZURE_SPECIAL_MATCHING_RULES,
    "description": "Microsoft Azure and Microsoft 365 Certifications",
    "website": "https://docs.microsoft.com/en-us/learn/certifications/"
}
