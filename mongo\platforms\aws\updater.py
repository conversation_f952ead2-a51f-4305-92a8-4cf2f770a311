"""
AWS certification platform updater implementation.
"""

import re
import sys
import os
from typing import List, Dict, Any, Optional

# Add parent directories to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
mongo_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, mongo_dir)

from platforms.base_platform import BasePlatform
from core.base_updater import BaseQNAUpdater
from platforms.aws.config import AWS_CONFIG


class AWSPlatform(BasePlatform):
    """
    AWS certification platform implementation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize AWS platform.
        
        Args:
            config: Optional configuration override
        """
        platform_config = config or AWS_CONFIG
        super().__init__(platform_config)
    
    def get_exam_codes(self) -> List[str]:
        """Get AWS exam codes."""
        return self.config['exam_codes']
    
    def get_provider_slug(self) -> str:
        """Get AWS provider slug."""
        return self.config['provider_slug']
    
    def normalize_exam_code(self, exam_code: str) -> str:
        """
        Normalize AWS exam code for URL matching.
        
        Args:
            exam_code: Raw AWS exam code
            
        Returns:
            Normalized exam code
        """
        return exam_code.replace("_", "-")
    
    def has_special_matching_rules(self, exam_code: str) -> bool:
        """
        Check if AWS exam code has special URL matching rules.
        
        Args:
            exam_code: AWS exam code to check
            
        Returns:
            True if special matching rules apply
        """
        return exam_code in self.config.get('special_matching_rules', {})
    
    def apply_special_matching(self, exam_code: str, url: str) -> bool:
        """
        Apply AWS-specific URL matching rules.
        
        Args:
            exam_code: AWS exam code being matched
            url: URL to check against
            
        Returns:
            True if URL matches with special rules
        """
        special_rules = self.config.get('special_matching_rules', {})
        
        if exam_code not in special_rules:
            return False
        
        rules = special_rules[exam_code]
        alternative_patterns = rules.get('alternative_patterns', [])
        
        for pattern in alternative_patterns:
            if pattern.lower() in url.lower():
                return True
        
        return False


class AWSUpdater(BaseQNAUpdater):
    """
    AWS-specific QNA updater implementation.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize AWS updater.
        
        Args:
            config: Optional configuration override
        """
        platform = AWSPlatform(config)
        super().__init__(platform, config)
    
    def get_platform_info(self) -> Dict[str, Any]:
        """
        Get AWS platform information.

        Returns:
            Dictionary with AWS platform details
        """
        base_info = self.platform.get_platform_info()
        base_info.update({
            'description': AWS_CONFIG.get('description', ''),
            'website': AWS_CONFIG.get('website', '')
        })
        return base_info
