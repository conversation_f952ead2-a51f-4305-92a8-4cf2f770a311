"""
Shared business logic for exam subjects processing and question classification.

This module contains the core business logic that was missing from the refactored
subjects.py, including GPT response validation, log_subject collection integration,
question classification workflow, and database update operations.
"""

import re
import time
import tiktoken
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pymongo import MongoClient, ReturnDocument, UpdateOne
import pytz

# Import shared utilities
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.gpt_client import send_to_gpt

# Define Hong Kong timezone
tz = pytz.timezone("Asia/Hong_Kong")

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
exam_db = client["exam"]
log_db = client["log"]
qna_db = client["qna"]


def get_exam_code_and_subjects_from_gpt(gpt_response: str) -> Tuple[Optional[str], List[str]]:
    """
    Extracts exam code and domains from GPT response in format "[exam code]|domain 1|domain 2"

    Args:
        gpt_response: Response from GPT API

    Returns:
        tuple: (exam_code: str, domains: list) or (None, []) if extraction fails
    """
    try:
        # Split and clean parts
        parts = [part.strip() for part in gpt_response.split("|")]

        # Validate format
        if len(parts) < 2:
            print(f"Error: Invalid format in GPT response. Expected 'code|domain1|domain2', got: '{gpt_response}'")
            return None, []

        exam_code = parts[0]
        domains = parts[1:]

        # Validate exam code is not empty
        if not exam_code:
            print("Error: Empty exam code in GPT response")
            return None, []

        # Validate at least one domain exists
        if not domains:
            print("Error: No domains found in GPT response")
            return None, []

        return exam_code, domains

    except Exception as e:
        print(f"Error extracting exam code and domains: {e}")
        return None, []


def get_log_subject(exam_code: str) -> Optional[Dict]:
    """
    Retrieve the most recent log_subject record for an exam code.
    
    Args:
        exam_code: Exam code to search for
        
    Returns:
        Log subject document or None if not found
    """
    try:
        log_subject = log_db["subjects"].find_one(
            {"code": {"$regex": exam_code, "$options": "i"}},
            sort=[("timestamp", -1)]  # Get most recent document
        )
        if log_subject:
            # print(f"Log subject found for {exam_code}")
            return log_subject
        else:
            print(f"No log subject found for {exam_code}")
            return None
    except Exception as e:
        print(f"Error retrieving log subject: {e}")
        return None


def validate_exam_details(exam_code: str, gpt_domains: List[str]) -> bool:
    """
    Validate GPT-extracted domains against existing MongoDB exam details.
    
    Args:
        exam_code: Exam code to validate
        gpt_domains: Domains extracted from GPT
        
    Returns:
        True if validation passes and no update needed, False otherwise
    """
    try:
        exam_detail = exam_db["detail"].find_one({
            "$or": [
                {"code": {"$regex": exam_code, "$options": "i"}},
                {"name": {"$regex": exam_code, "$options": "i"}}
            ]
        })

        if exam_detail:
            db_exam_code = exam_detail.get('exam_code') or []
            if db_exam_code != exam_code:
                print(f"Upserting valid/updated exam code {exam_code} to db")
                exam_detail = exam_db["detail"].find_one_and_update(
                    {
                        "$or": [
                            {"code": {"$regex": exam_code, "$options": "i"}},
                            {"name": {"$regex": exam_code, "$options": "i"}}
                        ]
                    },
                    {"$set": {"exam_code": exam_code}},
                    return_document=ReturnDocument.AFTER
                )

            exam_detail_subjects = exam_detail.get("subjects") or []
            if exam_detail_subjects == gpt_domains:
                print("Subjects match with db - no comparison needed")
                return True

        return False

    except Exception as e:
        print(f"Validation error: {e}")
        return False


def insert_log_subject_if_domains_differ(gpt_domains: List[str], exam_code: str) -> bool:
    """
    Insert new log_subject record if domains differ from existing record.
    
    Args:
        gpt_domains: Domains extracted from GPT
        exam_code: Exam code
        
    Returns:
        True if new record was inserted, False otherwise
    """
    try:
        gpt_domains = [d.strip() for d in gpt_domains if d.strip()]
        collection = log_db["subjects"]

        # Find the latest existing document with timestamp
        existing = get_log_subject(exam_code)

        needs_insert = False

        if existing:
            print(f"Existing domains: {existing.get('domains', [])}")
            existing_domains = [d for d in existing.get("domains", []) if d]

            # Check if domains are null or different
            if not existing_domains or set(existing_domains) != set(gpt_domains):
                needs_insert = True
                print("🔄 Domains differ or null - creating new record")
        else:
            needs_insert = True
            print("🆕 No existing document - creating new")

        if needs_insert:
            new_document = {
                "code": exam_code.upper(),
                "domains": gpt_domains,
                "questions": [],
                "timestamp": datetime.now(tz)
            }
            result = collection.insert_one(new_document)
            return result.inserted_id is not None

        print("✅ Domains match - no action needed")
        return False

    except Exception as e:
        print(f"Error updating log: {str(e)}")
        return False


def get_filtered_questions(exam_code: str, classified_q: List[Dict]) -> List[Dict]:
    """
    Get unclassified questions for batch processing.
    
    Args:
        qna_collection: MongoDB collection (amazon or microsoft)
        exam_code: Exam code to filter by
        classified_q: List of already classified questions
        
    Returns:
        List of unclassified questions
    """
    try:
        exam_detail = exam_db["detail"].find_one(
            {
                "$or": [
                    {"code": {"$regex": exam_code, "$options": "i"}},
                    {"name": {"$regex": exam_code, "$options": "i"}}
                ]
            })
        if not exam_detail:
            print(f"No exam details found for {exam_code}. Cannot get filtered questions.")
            return []

        print(f"Code in exam detail: {exam_detail.get('code')}")

        classified_urls = [item["url"] for item in classified_q]

        query = {"code": exam_detail.get('code')}
        if classified_urls:
            query["url"] = {"$nin": classified_urls}
            
        qna_collection = qna_db[exam_detail.get('vendor_code')]
        result_list = list(qna_collection.find(query).sort("_id", 1).limit(100))
        
        return result_list
    except Exception as e:
        print(f"Error retrieving filtered questions: {e}")
        return []


def update_log_subject_with_question_classification(exam_code: str, question_id: str, domain: str, task: float = None) -> bool:
    """
    Update log_subject with question classification result.

    Args:
        exam_code: Exam code
        question_id: Question ID/URL
        domain: Classified domain

    Returns:
        True if update successful, False otherwise
    """
    try:
        log_doc = get_log_subject(exam_code)
        if not log_doc:
            print(f"No log subject found for {exam_code}")
            return False

        # Check for existing entry first
        existing_questions = log_doc.get("questions", [])
        if any(q.get('url') == question_id for q in existing_questions):
            print(f"Skipping duplicate URL: {question_id}")
            return False

        log_entry = {
            "url": question_id,
            "domain": domain
        }

        # Add task field if provided (following amazon.txt format)
        if task is not None:
            log_entry["task"] = task

        # Use MongoDB $push to add the new entry directly to the database
        result = log_db["subjects"].update_one(
            {"_id": log_doc["_id"]},
            {
                "$push": {"questions": log_entry},
                "$set": {"timestamp": datetime.now(tz)}
            }
        )

        if result.modified_count > 0:
            #print(f"✅ Added classification for {question_id} -> domain {domain}")
            return True
        else:
            print(f"❌ Failed to add classification for {question_id}")
            return False

    except Exception as e:
        print(f"Error updating log subject with question classification: {e}")
        return False


def update_exam_detail_and_qna_domains_with_gpt_domains(exam_code: str, gpt_domains: List[str], platform: str = "aws") -> bool:
    """
    Update exam details and QNA domains with GPT-processed subjects.

    Args:
        exam_code: Exam code
        gpt_domains: Domains extracted from GPT
        platform: Platform (aws or azure)

    Returns:
        True if update successful, False otherwise
    """
    try:
        # First check if log subject exists
        log_subject = get_log_subject(exam_code)
        if not log_subject:
            print(f"No log subject found for {exam_code} - aborting update")
            return False

        # Proceed with updates since log exists
        exam_detail = exam_db["detail"].find_one(
            {
                "$or": [
                    {"code": {"$regex": exam_code, "$options": "i"}},
                    {"name": {"$regex": exam_code, "$options": "i"}}
                ]
            })
        if not exam_detail:
            print(f"No exam details found for {exam_code}")
            return False

        # Determine collection based on platform
        collection_name = "amazon" if platform == "aws" else "microsoft"
        qna_collection = qna_db[collection_name]

        qna_collection_operations = []

        print(f"Updating {exam_code} domains from log_subject to qna_collection...")

        # Process questions from the verified log subject
        for q_idx, question in enumerate(log_subject.get("questions", []), 1):
            try:
                url = question.get("url")
                domain = question.get("domain")

                if not url or not domain:
                    print(f"Skipping malformed question {q_idx}")
                    continue

                # Create update operation for Q&A collection
                qna_collection_operations.append(
                    UpdateOne(
                        {
                            "url": url,
                            "code": exam_detail["code"]
                        },
                        {
                            "$set": {
                                "subject": domain,
                                "timestamp_updated": datetime.now(tz)
                            }
                        },
                    )
                )

            except Exception as e:
                print(f"Error processing question {q_idx}: {str(e)}")
                continue

        # Bulk write if we have operations
        if qna_collection_operations:
            qna_collection.bulk_write(qna_collection_operations)

        # Update exam detail with GPT domains
        exam_db["detail"].update_one(
            {"_id": exam_detail["_id"]},
            {
                "$set": {
                    "subjects": gpt_domains,
                    "total_questions": len(log_subject.get("questions", [])),
                    "total_questions_mc_or_mc2": qna_collection.count_documents({
                        "code": exam_detail["code"],
                        "subject": {"$exists": True},
                        "type": {"$in": ['mc', 'mc2']}  # at the moment we just care about mc and mc2
                    }),
                    "timestamp_subjects_updated": datetime.now(tz)
                }
            }
        )

        print(f"Successfully updated both Q&A collection and exam details for {exam_code}")
        return True

    except Exception as e:
        print(f"Error updating exam detail and Q&A domains: {e}")
        return False


def process_question_classification_workflow(exam_code: str, gpt_domains: List[str], cleaned_text: str, platform: str = "aws") -> bool:
    """
    Complete question classification workflow with batch processing.

    Args:
        exam_code: Exam code
        gpt_domains: Domains extracted from GPT
        cleaned_text: Cleaned text from PDF/web scraping
        platform: Platform (aws or azure)

    Returns:
        True if workflow completed successfully, False otherwise
    """
    try:
        # Initialize token encoder
        encoder = tiktoken.encoding_for_model("gpt-4o-mini")

        total_processed = 0
        processed_count = 1

        while True:
            classified_q = []
            log_subject = get_log_subject(exam_code)
            if log_subject and "questions" in log_subject:
                classified_q = log_subject["questions"]
                print(f"Classified questions: {len(classified_q)}")

            qna_questions = get_filtered_questions(exam_code, classified_q)
            if not qna_questions:
                print("No more questions to process.")
                break

            print(f"\nFiltered questions: {len(qna_questions)}")
            print(f"\nProcessing batch {processed_count}")

            # Format questions for GPT processing
            filtered_q = []
            for i, q in enumerate(qna_questions):
                try:
                    # Format question with choices
                    question_text = q['exam']['question'].replace('\n', ' ').strip()
                    choices_str = ""
                    if 'choices' in q['exam'] and isinstance(q['exam']['choices'], list):
                        choices = []
                        for choice_dict in q['exam']['choices']:
                            for letter, text in choice_dict.items():
                                cleaned_choice = text.replace('\n', ' ').strip()
                                choices.append(f"{letter}: {cleaned_choice}")
                        choices_str = "Choices: " + ", ".join(choices)

                    filtered_q.append(f"Question {total_processed + i + 1}: {question_text}? {choices_str}")
                except KeyError:
                    print(f"Warning: Skipping malformed question {q.get('_id', 'unknown')}")
                    continue

            # Process questions with GPT using token limits
            cleaned_text_with_q = cleaned_text
            current_tokens = len(encoder.encode(cleaned_text_with_q))

            print(f"Initial token for cleaned_text: {current_tokens}")
            processed_in_batch = 0
            x = 0

            for question in filtered_q:
                cleaned_text_with_q_tokens = len(encoder.encode(cleaned_text_with_q + question))
                if cleaned_text_with_q_tokens > 3800:
                    print(f"Token limit reached. Processed {processed_in_batch} in this batch.")
                    break

                cleaned_text_with_q += question
                processed_in_batch += 1
                x += 1

            # Save debug file
            try:
                with open("cleaned_text_with_questions.txt", "w", encoding="utf-8") as f:
                    f.write(cleaned_text_with_q)
                print("Saved cleaned text with questions to 'cleaned_text_with_questions.txt'")
            except Exception as e:
                print(f"Error saving to text file: {e}")

            # Send to GPT and update log
            if processed_in_batch == 0:
                print("No questions to process. Exiting.")
                break

            # Create domain mapping for GPT prompt (1, 2, 3... format)
            domain_mapping = {}
            domain_list_text = ""
            for i, domain in enumerate(gpt_domains, 1):
                domain_mapping[str(i)] = domain
                domain_list_text += f"Domain {i}: {domain}\n"

            gpt_prompt = f"""Based on the exam content provided, classify each question to the appropriate domain number.

Available Domains:
{domain_list_text}

IMPORTANT: Number the questions starting from 1 for this batch, regardless of any previous context.

For each question in this batch, return only the question number and domain number in this format:
'Question X: Y' where X is the question number (starting from 1) and Y is the domain number (1, 2, 3, etc.)

Example for a batch of 3 questions:
Question 1: 2
Question 2: 1
Question 3: 3

Classify all questions provided, numbering them 1, 2, 3, etc. for this specific batch."""

            q_subject_response = send_to_gpt(cleaned_text_with_q, gpt_prompt)

            if not q_subject_response:
                print("Error: No GPT response for questions")
                continue

            # Parse classification results and update log_subject
            classification_lines = q_subject_response.strip().split('\n')
            for line in classification_lines:
                line = line.strip()
                if not line:
                    continue

                try:
                    # Parse the new format: "Question X: Y" where Y is domain number
                    match = re.match(r'Question\s+(\d+):\s*(\d+)', line, re.IGNORECASE)
                    if match:
                        question_num = int(match.group(1))
                        domain_num = match.group(2).strip()

                        # Fix: GPT returns question numbers 1, 2, 3... for each batch
                        # So question_num - 1 gives us the index within the current batch
                        question_index_in_batch = question_num - 1

                        if question_index_in_batch < len(qna_questions):
                            question_id = qna_questions[question_index_in_batch]['url']

                            # Calculate task number (domain.subtask format like 1.1, 2.3, etc.)
                            # For now, use domain_num.1 as default task
                            task_num = float(f"{domain_num}.1")

                            # Update log_subject with classification using numeric domain
                            update_log_subject_with_question_classification(exam_code, question_id, domain_num, task_num)
                        else:
                            print(f"Warning: Question index {question_index_in_batch} out of range for batch size {len(qna_questions)}")
                    else:
                        print(f"Warning: Could not parse line: {line}")

                except ValueError as e:
                    print(f"Error parsing question number: {e}")
                    continue
                except Exception as e:
                    print(f"Error processing classification: {e}")
                    continue

            total_processed += processed_in_batch
            processed_count += 1

            print(f"Processed {processed_in_batch} questions in this batch")
            print(f"Total processed so far: {total_processed}")

            # Break if we didn't process any questions in this batch
            if processed_in_batch == 0:
                print("No questions processed in this batch. Stopping.")
                break

            # Optional: Add a small delay between batches
            time.sleep(1)

        print(f"Classification complete. Total questions processed: {total_processed}")
        return True

    except Exception as e:
        print(f"Error in question classification workflow: {e}")
        return False
