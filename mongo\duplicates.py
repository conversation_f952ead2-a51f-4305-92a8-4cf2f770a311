import argparse
from pymongo import MongoClient
from collections import defaultdict

# MongoDB connection setup
client = MongoClient(
    "mongodb+srv://jnontoquine:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"
)
qna_db = client["qna"]

# List of exam codes to filter
exam_codes = [
    "aws-certified-advanced-networking-specialty-ans-c01",
    "aws-certified-ai-practitioner-aif-c01",
    "aws-certified-cloud-practitioner-clf-c02",
    "aws-certified-data-engineer-associate-dea-c01",
    "aws-certified-developer-associate-dva-c02",
    "aws-certified-devops-engineer-professional-dop-c02",
    "aws-certified-machine-learning-engineer-associate-mla-c01",
    "aws-certified-machine-learning-specialty",
    "aws-certified-security-specialty-scs-c02",
    "aws-certified-solutions-architect-associate-saa-c03",
    "aws-certified-solutions-architect-professional-sap-c02",
    "aws-certified-sysops-administrator-associate"
]

# Function to find duplicates by URL
def find_duplicates_by_url(collection, exam_codes):
    duplicates = defaultdict(list)
    for code in exam_codes:
        # Find all documents for the given exam code
        documents = collection.find({"code": code})
        url_count = defaultdict(int)
        
        # Count occurrences of each URL
        for doc in documents:
            url = doc.get("url")
            if url:
                url_count[url] += 1
                if url_count[url] > 1:
                    duplicates[url].append(doc)
    
    return duplicates

# Function to filter records with missing fields
def filter_missing_fields(records):
    return [record for record in records if "subject" not in record or "ai_explanation" not in record]

def main(is_delete):
    # Find duplicates
    duplicates = find_duplicates_by_url(qna_db["amazon"], exam_codes)

    # Filter duplicates with missing fields
    filtered_duplicates = {url: filter_missing_fields(records) for url, records in duplicates.items() if filter_missing_fields(records)}

    # Export filtered duplicates to a text file
    with open("filtered_duplicates.txt", "w", encoding="utf-8") as f:
        for url, records in filtered_duplicates.items():
            f.write(f"URL: {url}\n")
            for record in records:
                f.write(f"{record}\n")
            f.write("\n")

    print("Filtered duplicates exported to 'filtered_duplicates.txt'")

    # Add 'to_delete' field to filtered duplicates
    for url, records in filtered_duplicates.items():
        for record in records:
            qna_db["amazon"].update_one(
                {"_id": record["_id"]},
                {"$set": {"to_delete": 1}}
            )

    print("Added 'to_delete' field to filtered duplicates")

    # Delete records with 'to_delete' field if is_delete is 1
    if is_delete == 1:
        delete_result = qna_db["amazon"].delete_many({"to_delete": 1})
        print(f"Deleted {delete_result.deleted_count} records with 'to_delete=1'")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process duplicates and optionally delete them.")
    parser.add_argument("--is_delete", type=int, default=0, help="Set to 1 to delete records with 'is_delete=1'")
    args = parser.parse_args()
    main(args.is_delete)
